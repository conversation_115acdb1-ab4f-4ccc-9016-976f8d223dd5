# Order List Rating Information

## Example Response with Rating Info

### Scenario 1: 3 out of 4 products rated

```json
{
  "orders": [
    {
      "data": {
        "order_id": "12345-00-67890",
        "items": [
          {
            "name": "Product A",
            "product_id": 101,
            "sku": "SKU001",
            "is_reviewed": true
          },
          {
            "name": "Product B", 
            "product_id": 102,
            "sku": "SKU002",
            "is_reviewed": true
          },
          {
            "name": "Product C",
            "product_id": 103,
            "sku": "SKU003", 
            "is_reviewed": true
          },
          {
            "name": "Product D",
            "product_id": 104,
            "sku": "SKU004",
            "is_reviewed": false
          }
        ],
        "rate_info": {
          "rated_count": 3,
          "total_count": 4,
          "is_full_rated": false
        }
      }
    }
  ]
}
```

### Scenario 2: All 4 products rated (fully rated)

```json
{
  "orders": [
    {
      "data": {
        "order_id": "12345-00-67890",
        "items": [
          {
            "name": "Product A",
            "product_id": 101,
            "sku": "SKU001",
            "is_reviewed": true
          },
          {
            "name": "Product B",
            "product_id": 102,
            "sku": "SKU002", 
            "is_reviewed": true
          },
          {
            "name": "Product C",
            "product_id": 103,
            "sku": "SKU003",
            "is_reviewed": true
          },
          {
            "name": "Product D",
            "product_id": 104,
            "sku": "SKU004",
            "is_reviewed": true
          }
        ],
        "rate_info": {
          "rated_count": 4,
          "total_count": 4,
          "is_full_rated": true
        }
      }
    }
  ]
}
```

### Scenario 3: No products rated

```json
{
  "orders": [
    {
      "data": {
        "order_id": "12345-00-67890",
        "items": [
          {
            "name": "Product A",
            "product_id": 101,
            "sku": "SKU001",
            "is_reviewed": false
          },
          {
            "name": "Product B",
            "product_id": 102,
            "sku": "SKU002",
            "is_reviewed": false
          }
        ],
        "rate_info": {
          "rated_count": 0,
          "total_count": 2,
          "is_full_rated": false
        }
      }
    }
  ]
}
```

## Frontend Usage

```javascript
// Check rating status for each order
orders.forEach(order => {
  const { rated_count, total_count, is_full_rated } = order.data.rate_info;
  
  if (is_full_rated) {
    console.log(`Order ${order.data.order_id}: All products rated ✅`);
  } else {
    console.log(`Order ${order.data.order_id}: ${rated_count}/${total_count} products rated`);
    // Show "Rate remaining products" button
  }
  
  // Show individual product rating status
  order.data.items.forEach(item => {
    if (item.is_reviewed) {
      console.log(`${item.name}: Rated ⭐`);
    } else {
      console.log(`${item.name}: Not rated - show rating option`);
    }
  });
});
```

## Key Features

1. **`rate_info.rated_count`**: Number of products that have been rated
2. **`rate_info.total_count`**: Total number of products in the order
3. **`rate_info.is_full_rated`**: Boolean indicating if all products are rated
4. **`items[].is_reviewed`**: Individual product rating status

This allows the frontend to:
- Show "3/4 products rated" text
- Display "Rate remaining products" button when not fully rated
- Show individual product rating status
- Hide rating options when `is_full_rated: true`
