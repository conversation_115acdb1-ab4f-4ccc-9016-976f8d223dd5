# Order Tracking API Documentation

## Update Order Review Status Endpoint

### Endpoint

`POST /order-tracking/api/v1/admin/orders/update-review-status`

### Description

This endpoint updates the `is_reviewed` flag to `true` for ALL order items in the specified orders ONLY when the given product_id is NOT found in any of those orders. If the product_id exists in any of the orders, no updates are performed.

### Authentication

This endpoint requires an API key to be passed in the `x-api-key` header.

### Headers

```
x-api-key: YOUR_ADMIN_API_KEY
Content-Type: application/json
```

### Request Body

```json
{
  "product_id": 1,
  "order_ids": ["234234", "45645"],
  "customer_id": 123
}
```

### Request Body Parameters

- `product_id` (number, required): The ID of the product to mark as reviewed
- `order_ids` (string[], required): Array of order IDs to update
- `customer_id` (number, required): The customer ID that owns these orders

### Response

#### Success Response (Product NOT found in orders)

```json
{
  "success": true,
  "message": "Product not found in orders. Successfully marked 5 items as reviewed",
  "updated_items_count": 5,
  "affected_orders": ["234234", "45645"]
}
```

#### Success Response (Product found in orders - no update)

```json
{
  "success": false,
  "message": "Product found in orders. No items were marked as reviewed",
  "updated_items_count": 0,
  "affected_orders": ["234234", "45645"],
  "error": "Product with ID 1 exists in the specified orders"
}
```

### Response Parameters

- `success` (boolean): Whether the operation was successful
- `message` (string): Descriptive message about the operation
- `updated_items_count` (number): Number of order items that were updated
- `affected_orders` (string[]): Array of order IDs that were processed
- `error` (string, optional): Error message if the operation failed

### Error Responses

#### 401 Unauthorized

```json
{
  "statusCode": 401,
  "message": "Invalid API key"
}
```

#### 400 Bad Request

```json
{
  "success": false,
  "message": "No orders found for the given criteria",
  "updated_items_count": 0,
  "affected_orders": [],
  "error": "Orders not found or do not belong to the specified customer"
}
```

### Example Usage

#### cURL

```bash
curl -X POST \
  http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status \
  -H 'x-api-key: YOUR_ADMIN_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "product_id": 1,
    "order_ids": ["234234", "45645"],
    "customer_id": 123
  }'
```

#### JavaScript/Node.js

```javascript
const response = await fetch(
  'http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status',
  {
    method: 'POST',
    headers: {
      'x-api-key': 'YOUR_ADMIN_API_KEY',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      product_id: 1,
      order_ids: ['234234', '45645'],
      customer_id: 123,
    }),
  },
);

const result = await response.json();
console.log(result);
```

### Environment Variables

Make sure to set the `ADMIN_API_KEY` environment variable for the API key validation to work:

```bash
ADMIN_API_KEY=your_secure_api_key_here
```

### Database Changes

This endpoint adds an `is_reviewed` column to the `order_tracking_item` table with a default value of `false`. When this endpoint is called:

- If the specified `product_id` is NOT found in any of the order items, it sets `is_reviewed` to `true` for ALL items in those orders
- If the specified `product_id` IS found in any of the order items, no updates are performed

### Logic Summary

The endpoint implements a "negative match" logic:

1. Check if the given `product_id` exists in any items within the specified orders
2. If NO items match the `product_id`, mark ALL items in those orders as reviewed (`is_reviewed = true`)
3. If ANY items match the `product_id`, do nothing and return an error response

This is useful for scenarios where you want to mark orders as reviewed only when they don't contain a specific product.

---

## Get Latest Delivered Order for Rating Modal

### Endpoint

`GET /order-tracking/api/v1/orders/latest-delivered`

### Description

This endpoint retrieves the latest delivered order for a customer and determines whether to show a rating modal based on whether any products in that order have been reviewed.

### Authentication

This endpoint requires a Bearer token in the Authorization header.

### Headers

```
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json
```

### Response

#### Success Response (Show Rating Modal)

```json
{
  "success": true,
  "show_rating_modal": true,
  "order": {
    "order_id": "12345-00-67890",
    "delivered_at": "2024-01-15T10:30:00.000Z",
    "grand_total": 1500.0,
    "line_item_count": 2,
    "items": [
      {
        "product_id": 1,
        "sku": "SKU001",
        "name": "Product 1",
        "image": "image1.jpg",
        "ordered_qty": 1,
        "is_reviewed": false
      },
      {
        "product_id": 2,
        "sku": "SKU002",
        "name": "Product 2",
        "image": "image2.jpg",
        "ordered_qty": 2,
        "is_reviewed": false
      }
    ]
  },
  "message": "Latest delivered order found - show rating modal"
}
```

#### Success Response (Don't Show Rating Modal)

```json
{
  "success": true,
  "show_rating_modal": false,
  "order": {
    "order_id": "12345-00-67890",
    "delivered_at": "2024-01-15T10:30:00.000Z",
    "grand_total": 1500.0,
    "line_item_count": 2,
    "items": [
      {
        "product_id": 1,
        "sku": "SKU001",
        "name": "Product 1",
        "image": "image1.jpg",
        "ordered_qty": 1,
        "is_reviewed": true
      },
      {
        "product_id": 2,
        "sku": "SKU002",
        "name": "Product 2",
        "image": "image2.jpg",
        "ordered_qty": 2,
        "is_reviewed": false
      }
    ]
  },
  "message": "Latest delivered order found - products already reviewed"
}
```

#### No Delivered Orders

```json
{
  "success": true,
  "show_rating_modal": false,
  "message": "No delivered orders found"
}
```

### Response Parameters

- `success` (boolean): Whether the operation was successful
- `show_rating_modal` (boolean): Whether to show the rating modal to the user
- `order` (object, optional): Latest delivered order details
  - `order_id` (string): Order identifier
  - `delivered_at` (string): ISO date when order was delivered
  - `grand_total` (number): Total order amount
  - `line_item_count` (number): Number of items in the order
  - `items` (array): Array of order items with review status
- `message` (string): Descriptive message about the result

### Logic

1. Authenticates user via Bearer token
2. Finds the latest delivered order (sorted by `deliveredAt` DESC)
3. Checks if any product in that order has `is_reviewed = true`
4. If NO products are reviewed → `show_rating_modal = true`
5. If ANY product is reviewed → `show_rating_modal = false`

### Example Usage

#### cURL

```bash
curl -X GET \
  http://localhost:3000/order-tracking/api/v1/orders/latest-delivered \
  -H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
  -H 'Content-Type: application/json'
```

#### JavaScript/Node.js

```javascript
const response = await fetch(
  'http://localhost:3000/order-tracking/api/v1/orders/latest-delivered',
  {
    method: 'GET',
    headers: {
      Authorization: 'Bearer YOUR_AUTH_TOKEN',
      'Content-Type': 'application/json',
    },
  },
);

const result = await response.json();

if (result.show_rating_modal) {
  // Show rating modal to user
  console.log('Show rating modal for order:', result.order.order_id);
} else {
  // Don't show modal
  console.log('No need to show rating modal');
}
```
