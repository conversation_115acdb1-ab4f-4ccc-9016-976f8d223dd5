# Order Tracking API Documentation

## Update Order Review Status Endpoint

### Endpoint
`POST /order-tracking/api/v1/admin/orders/update-review-status`

### Description
This endpoint updates the `is_reviewed` flag to `true` for order items that match the specified product_id within the given orders for a specific customer.

### Authentication
This endpoint requires an API key to be passed in the `x-api-key` header.

### Headers
```
x-api-key: YOUR_ADMIN_API_KEY
Content-Type: application/json
```

### Request Body
```json
{
  "product_id": 1,
  "order_ids": ["234234", "45645"],
  "customer_id": 123
}
```

### Request Body Parameters
- `product_id` (number, required): The ID of the product to mark as reviewed
- `order_ids` (string[], required): Array of order IDs to update
- `customer_id` (number, required): The customer ID that owns these orders

### Response
```json
{
  "success": true,
  "message": "Successfully updated review status for 2 items",
  "updated_items_count": 2,
  "affected_orders": ["234234", "45645"]
}
```

### Response Parameters
- `success` (boolean): Whether the operation was successful
- `message` (string): Descriptive message about the operation
- `updated_items_count` (number): Number of order items that were updated
- `affected_orders` (string[]): Array of order IDs that were processed
- `error` (string, optional): Error message if the operation failed

### Error Responses

#### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Invalid API key"
}
```

#### 400 Bad Request
```json
{
  "success": false,
  "message": "No orders found for the given criteria",
  "updated_items_count": 0,
  "affected_orders": [],
  "error": "Orders not found or do not belong to the specified customer"
}
```

### Example Usage

#### cURL
```bash
curl -X POST \
  http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status \
  -H 'x-api-key: YOUR_ADMIN_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "product_id": 1,
    "order_ids": ["234234", "45645"],
    "customer_id": 123
  }'
```

#### JavaScript/Node.js
```javascript
const response = await fetch('http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status', {
  method: 'POST',
  headers: {
    'x-api-key': 'YOUR_ADMIN_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    product_id: 1,
    order_ids: ['234234', '45645'],
    customer_id: 123
  })
});

const result = await response.json();
console.log(result);
```

### Environment Variables
Make sure to set the `ADMIN_API_KEY` environment variable for the API key validation to work:

```bash
ADMIN_API_KEY=your_secure_api_key_here
```

### Database Changes
This endpoint adds an `is_reviewed` column to the `order_tracking_item` table with a default value of `false`. When this endpoint is called, it sets this flag to `true` for matching items.
