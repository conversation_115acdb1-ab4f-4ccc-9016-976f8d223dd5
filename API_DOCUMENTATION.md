# Order Tracking API Documentation

## Update Order Review Status Endpoint

### Endpoint

`POST /order-tracking/api/v1/admin/orders/update-review-status`

### Description

This endpoint updates the `is_reviewed` flag to `true` for order items that match the specified product_id within the given orders for a specific customer.

### Authentication

This endpoint requires an API key to be passed in the `x-api-key` header.

### Headers

```
x-api-key: YOUR_ADMIN_API_KEY
Content-Type: application/json
```

### Request Body

```json
{
  "product_id": 1,
  "order_ids": ["234234", "45645"],
  "customer_id": 123
}
```

### Request Body Parameters

- `product_id` (number, required): The ID of the product to mark as reviewed
- `order_ids` (string[], required): Array of order IDs to update
- `customer_id` (number, required): The customer ID that owns these orders

### Response

#### Success Response (Product found and updated)

```json
{
  "success": true,
  "message": "Successfully marked 2 items as reviewed",
  "updated_items_count": 2,
  "affected_orders": ["234234", "45645"]
}
```

#### Error Response (Product not found in orders)

```json
{
  "success": false,
  "message": "No items found with the specified product ID",
  "updated_items_count": 0,
  "affected_orders": ["234234", "45645"],
  "error": "Product with ID 1 not found in the specified orders"
}
```

### Response Parameters

- `success` (boolean): Whether the operation was successful
- `message` (string): Descriptive message about the operation
- `updated_items_count` (number): Number of order items that were updated
- `affected_orders` (string[]): Array of order IDs that were processed
- `error` (string, optional): Error message if the operation failed

### Error Responses

#### 401 Unauthorized

```json
{
  "statusCode": 401,
  "message": "Invalid API key"
}
```

#### 400 Bad Request

```json
{
  "success": false,
  "message": "No orders found for the given criteria",
  "updated_items_count": 0,
  "affected_orders": [],
  "error": "Orders not found or do not belong to the specified customer"
}
```

### Example Usage

#### cURL

```bash
curl -X POST \
  http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status \
  -H 'x-api-key: YOUR_ADMIN_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "product_id": 1,
    "order_ids": ["234234", "45645"],
    "customer_id": 123
  }'
```

#### JavaScript/Node.js

```javascript
const response = await fetch(
  'http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status',
  {
    method: 'POST',
    headers: {
      'x-api-key': 'YOUR_ADMIN_API_KEY',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      product_id: 1,
      order_ids: ['234234', '45645'],
      customer_id: 123,
    }),
  },
);

const result = await response.json();
console.log(result);
```

### Environment Variables

Make sure to set the `ADMIN_API_KEY` environment variable for the API key validation to work:

```bash
ADMIN_API_KEY=your_secure_api_key_here
```

### Database Changes

This endpoint adds an `is_reviewed` column to the `order_tracking_item` table with a default value of `false`. When this endpoint is called:

- If the specified `product_id` is NOT found in any of the order items, it sets `is_reviewed` to `true` for ALL items in those orders
- If the specified `product_id` IS found in any of the order items, no updates are performed

### Logic Summary

The endpoint implements a direct product review logic:

1. Find all order items that match the given `product_id` within the specified orders
2. Update `is_reviewed = true` for those matching items
3. If no items match the `product_id`, return an error response
4. If items are found and updated, return success with count of updated items

This is useful for marking specific products as reviewed after a customer has provided feedback for that particular product.

---

## Get Latest Delivered Order for Rating Modal

### Endpoint

`GET /order-tracking/api/v1/orders/latest-delivered`

### Description

This endpoint retrieves the latest delivered order for a customer and determines whether to show a rating modal based on whether any products in that order have been reviewed.

### Authentication

This endpoint requires a Bearer token in the Authorization header.

### Headers

```
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json
```

### Response

#### Success Response (Show Rating Modal)

```json
{
  "success": true,
  "show_rating_modal": true,
  "order_id": "12345-00-67890",
  "delivered_at": "2024-01-15T10:30:00.000Z",
  "item_count": 2,
  "message": "Latest delivered order found - show rating modal"
}
```

#### Success Response (Don't Show Rating Modal)

```json
{
  "success": true,
  "show_rating_modal": false,
  "order_id": "12345-00-67890",
  "delivered_at": "2024-01-15T10:30:00.000Z",
  "item_count": 2,
  "message": "Latest delivered order found - products already reviewed"
}
```

#### No Delivered Orders

```json
{
  "success": true,
  "show_rating_modal": false,
  "message": "No delivered orders found"
}
```

### Response Parameters

- `success` (boolean): Whether the operation was successful
- `show_rating_modal` (boolean): Whether to show the rating modal to the user
- `order_id` (string, optional): Order identifier of the latest delivered order
- `delivered_at` (string, optional): ISO date when order was delivered
- `item_count` (number, optional): Number of items in the order
- `message` (string): Descriptive message about the result

### Logic

1. Authenticates user via Bearer token
2. Finds the latest delivered order (sorted by `deliveredAt` DESC)
3. Checks if any product in that order has `is_reviewed = true`
4. If NO products are reviewed → `show_rating_modal = true`
5. If ANY product is reviewed → `show_rating_modal = false`

### Example Usage

#### cURL

```bash
curl -X GET \
  http://localhost:3000/order-tracking/api/v1/orders/latest-delivered \
  -H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
  -H 'Content-Type: application/json'
```

#### JavaScript/Node.js

```javascript
const response = await fetch(
  'http://localhost:3000/order-tracking/api/v1/orders/latest-delivered',
  {
    method: 'GET',
    headers: {
      Authorization: 'Bearer YOUR_AUTH_TOKEN',
      'Content-Type': 'application/json',
    },
  },
);

const result = await response.json();

if (result.show_rating_modal) {
  // Show rating modal to user
  console.log('Show rating modal for order:', result.order_id);
  console.log('Delivered on:', result.delivered_at);
  console.log('Item count:', result.item_count);
} else {
  // Don't show modal
  console.log('No need to show rating modal');
}
```
