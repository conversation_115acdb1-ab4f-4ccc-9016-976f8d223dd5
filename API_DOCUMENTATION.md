# Order Tracking API Documentation

## Update Order Review Status Endpoint

### Endpoint

`POST /order-tracking/api/v1/admin/orders/update-review-status`

### Description

This endpoint updates the `is_reviewed` flag to `true` for ALL order items in the specified orders ONLY when the given product_id is NOT found in any of those orders. If the product_id exists in any of the orders, no updates are performed.

### Authentication

This endpoint requires an API key to be passed in the `x-api-key` header.

### Headers

```
x-api-key: YOUR_ADMIN_API_KEY
Content-Type: application/json
```

### Request Body

```json
{
  "product_id": 1,
  "order_ids": ["234234", "45645"],
  "customer_id": 123
}
```

### Request Body Parameters

- `product_id` (number, required): The ID of the product to mark as reviewed
- `order_ids` (string[], required): Array of order IDs to update
- `customer_id` (number, required): The customer ID that owns these orders

### Response

#### Success Response (Product NOT found in orders)

```json
{
  "success": true,
  "message": "Product not found in orders. Successfully marked 5 items as reviewed",
  "updated_items_count": 5,
  "affected_orders": ["234234", "45645"]
}
```

#### Success Response (Product found in orders - no update)

```json
{
  "success": false,
  "message": "Product found in orders. No items were marked as reviewed",
  "updated_items_count": 0,
  "affected_orders": ["234234", "45645"],
  "error": "Product with ID 1 exists in the specified orders"
}
```

### Response Parameters

- `success` (boolean): Whether the operation was successful
- `message` (string): Descriptive message about the operation
- `updated_items_count` (number): Number of order items that were updated
- `affected_orders` (string[]): Array of order IDs that were processed
- `error` (string, optional): Error message if the operation failed

### Error Responses

#### 401 Unauthorized

```json
{
  "statusCode": 401,
  "message": "Invalid API key"
}
```

#### 400 Bad Request

```json
{
  "success": false,
  "message": "No orders found for the given criteria",
  "updated_items_count": 0,
  "affected_orders": [],
  "error": "Orders not found or do not belong to the specified customer"
}
```

### Example Usage

#### cURL

```bash
curl -X POST \
  http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status \
  -H 'x-api-key: YOUR_ADMIN_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "product_id": 1,
    "order_ids": ["234234", "45645"],
    "customer_id": 123
  }'
```

#### JavaScript/Node.js

```javascript
const response = await fetch(
  'http://localhost:3000/order-tracking/api/v1/admin/orders/update-review-status',
  {
    method: 'POST',
    headers: {
      'x-api-key': 'YOUR_ADMIN_API_KEY',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      product_id: 1,
      order_ids: ['234234', '45645'],
      customer_id: 123,
    }),
  },
);

const result = await response.json();
console.log(result);
```

### Environment Variables

Make sure to set the `ADMIN_API_KEY` environment variable for the API key validation to work:

```bash
ADMIN_API_KEY=your_secure_api_key_here
```

### Database Changes

This endpoint adds an `is_reviewed` column to the `order_tracking_item` table with a default value of `false`. When this endpoint is called:

- If the specified `product_id` is NOT found in any of the order items, it sets `is_reviewed` to `true` for ALL items in those orders
- If the specified `product_id` IS found in any of the order items, no updates are performed

### Logic Summary

The endpoint implements a "negative match" logic:

1. Check if the given `product_id` exists in any items within the specified orders
2. If NO items match the `product_id`, mark ALL items in those orders as reviewed (`is_reviewed = true`)
3. If ANY items match the `product_id`, do nothing and return an error response

This is useful for scenarios where you want to mark orders as reviewed only when they don't contain a specific product.
