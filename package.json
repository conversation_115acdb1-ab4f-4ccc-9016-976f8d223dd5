{"name": "sinform-node-order-microservices", "version": "0.0.1", "description": "", "author": "<PERSON><PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "rimraf dist && rimraf src/graphql.ts", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "rimraf src/graphql.ts && nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:up": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo"}, "dependencies": {"@lumigo/opentelemetry": "^1.0.2", "@nestjs/apollo": "^10.0.8", "@nestjs/axios": "^0.0.7", "@nestjs/common": "^8.0.0", "@nestjs/core": "^8.0.0", "@nestjs/graphql": "^10.0.8", "@nestjs/platform-express": "^8.0.0", "@nestjs/schedule": "^2.2.3", "@nestjs/swagger": "^5.2.1", "@types/amqplib": "^0.10.1", "@types/sequelize": "^4.28.11", "amqplib": "^0.10.3", "apollo-server-core": "^3.6.7", "apollo-server-express": "^3.6.7", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "crypto-js": "^4.2.0", "dotenv": "^16.0.0", "ejs": "^3.1.7", "graphql": "^16.6.0", "graphql-tools": "^8.2.4", "mysql2": "^2.3.3", "nanoid": "^3.3.3", "razorpay": "^2.8.3", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "sequelize": "^6.17.0", "sequelize-typescript": "^2.1.3", "swagger": "^0.7.5", "swagger-ui": "^4.10.3", "swagger-ui-express": "^4.3.0", "ts-morph": "^13.0.3", "winston": "^3.6.0", "winston-daily-rotate-file": "^4.6.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^8.0.0", "@types/ejs": "^3.1.0", "@types/express": "^4.17.13", "@types/jest": "27.4.0", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.2.5", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^5.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}