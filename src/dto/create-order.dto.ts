import {
  IsString,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

class CustomAttributeDto {
  @IsString()
  attribute_code: string;

  @IsString()
  value: string;
}

export class CreateOrderDto {
  @IsString()
  @IsNotEmpty()
  cart_id: string;

  @IsString()
  @IsNotEmpty()
  payment_method: string;

  @IsBoolean()
  @IsOptional()
  is_buy_now: boolean;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CustomAttributeDto)
  custom_attributes: CustomAttributeDto[];
}
