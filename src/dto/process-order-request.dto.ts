import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  ArrayMinSize,
  ArrayMaxSize,
  IsOptional,
} from 'class-validator';

export class ProcessOrderPayload {
  @ApiProperty()
  @IsOptional()
  @IsString()
  status: string;

  @ApiProperty()
  @IsOptional()
  @ArrayMinSize(1, {
    message: 'please provide atleast one value in orderId array',
  })
  @ArrayMaxSize(5, { message: "orderid more than five can't processed" })
  @IsString({ each: true })
  @IsArray()
  orderId: string[];
}
