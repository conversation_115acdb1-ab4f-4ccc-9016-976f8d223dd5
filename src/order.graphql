type dkPlaceOrderOutputV2 {
  amount: Float!
  currency: String!
  merchant_id: String
  order_number: String!
  payment_method: String!
  reference_number: String
}

type updateStatusOutput {
  message: String
}
input dkPlaceOrderInputV2 {
  cart_id: String!
  payment_method: String!
  extension_attributes: [ExtensionAttributeInput]
}

input updateOrderStatusInput {
  order_id: String!
  status: String!
}

input ExtensionAttributeInput {
  attribute_code: String!
  value: String!
}

type Mutation {
  dkplaceOrderV2(input: dkPlaceOrderInputV2): dkPlaceOrderOutputV2
  updateOrderStatus(input: updateOrderStatusInput): updateStatusOutput
  buyNowPlaceOrder(input: dkPlaceOrderInputV2): dkPlaceOrderOutputV2
}

type OrderProductItem {
  can_return: Boolean
  name: String
  price: Float
  product_id: Int
  qty: Int
  returnable_qty: Float
  rewardpoints: String
  sku: String
  thumbnail: String
  url_key: String
}

type CustomerOrder {
  can_cancel: Boolean
  can_reorder: Boolean
  can_return: Boolean
  created_at: String
  currency: String
  grand_total: Float
  id: Int
  increment_id: String
  is_processing: Boolean
  items: [OrderProductItem]
  payment_method: String
  status: String
}

type dkCustomerOrder {
  can_cancel: Boolean
  can_reorder: Boolean
  can_return: Boolean
  created_at: String
  currency: String
  grand_total: Float
  id: Int
  increment_id: String
  is_processing: Boolean
  items: [OrderProductItem]
  status: String
}

type dkCustomerOrders {
  orders: [dkCustomerOrder]
}

input FetchOrderInputV2 {
  order_id: String!
  rzp_order_id: String
  rzp_payment_id: String
}

type FetchOrderOutputV2 {
  amount: Float
  can_refetch: Boolean
  can_retry_payment: Boolean
  currency: String
  currency_symbol: String
  error_msg: String
  failure_wait_time: String
  merchant_id: String
  order_created_at: String
  order_detail_available: Boolean
  order_id: String
  reference_number: String
  status: String
}

type Query {
  dkcustomerOrdersV2(timespan: Int): dkCustomerOrders
  customerOrdersV2(timespan: Int): [CustomerOrder]
  fetchOrderV2(input: FetchOrderInputV2!): FetchOrderOutputV2
  fetchOrderGuestV2(
    email: String!
    input: FetchOrderInputV2!
  ): FetchOrderOutputV2
  checkCustomerBoughtProduct(product_id: Int!, type: String): Boolean
}
