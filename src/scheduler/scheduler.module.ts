import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { PaymentTimeoutSchedulerService } from './payment-timeout-scheduler.service';
import { SchedulerController } from './scheduler.controller';
import { DatabaseModule } from '../database/database.module';
import { UtilsModule } from '../utils/utils.module';

@Module({
  imports: [ScheduleModule.forRoot(), DatabaseModule, UtilsModule],
  controllers: [SchedulerController],
  providers: [PaymentTimeoutSchedulerService],
  exports: [PaymentTimeoutSchedulerService],
})
export class SchedulerModule {}
