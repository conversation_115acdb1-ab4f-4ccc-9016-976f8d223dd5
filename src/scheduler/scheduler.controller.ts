import {
  Controller,
  Get,
  InternalServerErrorException,
  Post,
} from '@nestjs/common';
import { PaymentTimeoutSchedulerService } from './payment-timeout-scheduler.service';
import { logger } from '../utils/service-logger';

/**
 * Controller for scheduler-related endpoints
 * These endpoints are for monitoring and manual testing purposes
 */
@Controller('scheduler')
export class SchedulerController {
  constructor(
    private readonly paymentTimeoutSchedulerService: PaymentTimeoutSchedulerService,
  ) {}

  /**
   * Get statistics about payment pending orders
   * GET /scheduler/payment-pending-stats
   */
  @Get('payment-pending-stats')
  async getPaymentPendingStats() {
    try {
      const stats =
        await this.paymentTimeoutSchedulerService.getPaymentPendingStats();
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Error getting payment pending stats:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Manually trigger payment timeout check
   * POST /scheduler/manual-payment-timeout-check
   *
   * Note: This endpoint should be protected in production
   * Consider adding authentication/authorization guards
   */
  @Post('manual-payment-timeout-check')
  async manualPaymentTimeoutCheck() {
    try {
      logger.info('Manual payment timeout check triggered via API');
      const result =
        await this.paymentTimeoutSchedulerService.manualPaymentTimeoutCheck();

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Error in manual payment timeout check:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('run-schdeuler')
  async runScheduler() {
    try {
      const result =
        await this.paymentTimeoutSchedulerService.handlePaymentTimeoutCheck();
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      throw new InternalServerErrorException(error?.message);
    }
  }
}
