import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { Op } from 'sequelize';
import { SalesOrder } from '../database/entities/sales-order';
import { SalesOrderAddress } from '../database/entities/sales-order-address';
import { SalesOrderItem } from '../database/entities/sales-order-item';
import { SalesOrderPayment } from '../database/entities/sales-order-payment';
import { SalesOrderExtraInfo } from '../database/entities/sales-order-extra-info';
import { OrderItemExtraInfo } from '../database/entities/order-item-extra-info';
import { OrderStatuses } from '../config/constants';
import { NotifyERP } from '../utils/notify-erp';
import { logger } from '../utils/service-logger';

@Injectable()
export class PaymentTimeoutSchedulerService {
  constructor(private readonly notifyERP: NotifyERP) {}

  /**
   * Scheduled task that runs twice daily at 00:00 and 12:00
   * to check for payment pending orders older than 24 hours
   * and update their status to payment_failed
   * comment schdeluer in satging evironment
   */
  // @Cron('0 0,12 * * *', {
  //   name: 'payment-timeout-check',
  //   timeZone: 'Asia/Kolkata', // IST timezone
  // })
  async handlePaymentTimeoutCheck(): Promise<void> {
    const startTime = Date.now();
    logger.info('Starting payment timeout check scheduler');

    try {
      // Calculate the cutoff time (24 hours ago)
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - 24);

      // Find all orders with PAYMENT_PENDING status older than 24 hours
      const expiredOrders = await this.findExpiredPaymentPendingOrders(
        cutoffTime,
      );

      if (expiredOrders.length === 0) {
        logger.info('No expired payment pending orders found');
        return;
      }

      logger.info(
        `Found ${expiredOrders.length} expired payment pending orders`,
      );

      // Process orders in batches to avoid overwhelming the database
      const batchSize = 50;
      let processedCount = 0;
      let failedCount = 0;

      for (let i = 0; i < expiredOrders.length; i += batchSize) {
        const batch = expiredOrders.slice(i, i + batchSize);
        const batchResults = await this.processBatch(batch);

        processedCount += batchResults.processed;
        failedCount += batchResults.failed;
      }

      const duration = Date.now() - startTime;
      logger.info(
        `Payment timeout check completed. Processed: ${processedCount}, Failed: ${failedCount}, Duration: ${duration}ms`,
      );
    } catch (error) {
      logger.error('Error in payment timeout check scheduler:', error);
      throw error;
    }
  }

  /**
   * Find orders with PAYMENT_PENDING status older than the cutoff time
   * Uses optimized query with proper indexing considerations
   * Condition: createdAt <= cutoffTime (which means >= 24 hours old)
   */
  private async findExpiredPaymentPendingOrders(
    cutoffTime: Date,
  ): Promise<SalesOrder[]> {
    try {
      logger.info(
        `Searching for orders with status ${
          OrderStatuses.PAYMENT_PENDING
        } created before ${cutoffTime.toISOString()}`,
      );

      const orders = await SalesOrder.findAll({
        where: {
          status: OrderStatuses.PAYMENT_PENDING,
          createdAt: {
            [Op.lte]: cutoffTime, // Use lte (less than or equal) for >= 24 hours condition
          },
        },
        include: [
          SalesOrderPayment,
          SalesOrderAddress,
          {
            model: SalesOrderExtraInfo,
            attributes: [
              'registration_no',
              'exp_delivery_days',
              'exp_dispatch_days',
              'processed_at',
              'extra_info_entity_id',
            ],
          },
          {
            model: SalesOrderItem,
            include: [
              {
                model: OrderItemExtraInfo,
                required: false,
                attributes: ['referral_code', 'is_free_product'],
              },
            ],
          },
        ],
        order: [['createdAt', 'ASC']], // Process oldest orders first
        limit: 1000, // Limit to prevent memory issues with very large datasets
      });

      logger.info(
        `Found ${orders.length} orders with payment_pending status older than 24 hours`,
      );

      return orders;
    } catch (error) {
      logger.error('Error finding expired payment pending orders:', error);
      throw error;
    }
  }

  /**
   * Process a batch of orders to update their status to PAYMENT_FAILED
   * Uses bulk update for better performance
   */
  private async processBatch(
    orders: SalesOrder[],
  ): Promise<{ processed: number; failed: number }> {
    if (orders.length === 0) {
      return { processed: 0, failed: 0 };
    }

    const startTime = Date.now();
    const orderIds = orders.map((order) => order.order_id);
    const orderIncrementIds = orders.map((order) => order.increment_id);

    try {
      // Log batch start with analytics
      logger.info(`Starting batch update for ${orders.length} orders`, {
        batch_size: orders.length,
        order_ids: orderIncrementIds,
        analytics_key: 'system_batch_payment_failure_start',
        initiated_by: 'system_scheduler',
      });

      // Perform bulk update - much more efficient than individual updates
      const updateResult = await this.bulkUpdateOrdersToPaymentFailed(orderIds);

      if (updateResult.affectedRows !== orders.length) {
        logger.warn(
          `Bulk update affected ${updateResult.affectedRows} rows but expected ${orders.length}`,
          {
            expected: orders.length,
            actual: updateResult.affectedRows,
            order_ids: orderIncrementIds,
            analytics_key: 'system_batch_update_mismatch',
          },
        );
      }

      // Send notifications for successfully updated orders
      const notificationResults = await this.sendBatchNotifications(orders);

      const duration = Date.now() - startTime;

      // Log batch completion with analytics
      logger.info(`Batch update completed successfully`, {
        batch_size: orders.length,
        affected_rows: updateResult.affectedRows,
        notifications_sent: notificationResults.successful,
        notifications_failed: notificationResults.failed,
        processing_duration_ms: duration,
        analytics_key: 'system_batch_payment_failure_completed',
        initiated_by: 'system_scheduler',
        order_ids: orderIncrementIds,
      });

      return {
        processed: updateResult.affectedRows,
        failed: orders.length - updateResult.affectedRows,
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error(`Batch update failed`, {
        batch_size: orders.length,
        processing_duration_ms: duration,
        error_message: error.message,
        analytics_key: 'system_batch_payment_failure_error',
        initiated_by: 'system_scheduler',
        order_ids: orderIncrementIds,
      });

      // Fallback to individual updates if bulk update fails
      return await this.fallbackToIndividualUpdates(orders);
    }
  }

  /**
   * Bulk update multiple orders to PAYMENT_FAILED status
   * Much more efficient than individual updates
   */
  private async bulkUpdateOrdersToPaymentFailed(
    orderIds: number[],
  ): Promise<{ affectedRows: number }> {
    try {
      const updateData: any = {
        status: OrderStatuses.PAYMENT_FAILED,
        failed_by: 'system_scheduler',
        failure_timestamp: new Date(),
      };

      const [affectedRows] = await SalesOrder.update(updateData, {
        where: {
          order_id: {
            [Op.in]: orderIds,
          },
          status: OrderStatuses.PAYMENT_PENDING, // Additional safety check
        },
      });

      return { affectedRows };
    } catch (error) {
      logger.error('Error in bulk update operation:', error);
      throw error;
    }
  }

  /**
   * Send notifications for a batch of orders
   * Processes notifications concurrently for better performance
   */
  private async sendBatchNotifications(
    orders: SalesOrder[],
  ): Promise<{ successful: number; failed: number }> {
    let successful = 0;
    let failed = 0;

    // Process notifications concurrently but with a limit to avoid overwhelming the system
    const concurrencyLimit = 10;
    const chunks = [];

    for (let i = 0; i < orders.length; i += concurrencyLimit) {
      chunks.push(orders.slice(i, i + concurrencyLimit));
    }

    for (const chunk of chunks) {
      const promises = chunk.map(async (order: SalesOrder) => {
        try {
          // Update order status for notification
          order.status = OrderStatuses.PAYMENT_FAILED;

          await this.notifyERP.notifyStatusUpdate(
            order,
            order.items || [],
            order.address || [],
            order.payment,
            OrderStatuses.PAYMENT_FAILED,
            order.sales_order_extra_info,
            true, // Enable email notification
          );

          return { success: true, order_id: order.increment_id };
        } catch (error) {
          logger.error(
            `Failed to send notification for order ${order.increment_id}:`,
            error,
          );
          return {
            success: false,
            order_id: order.increment_id,
            error: error.message,
          };
        }
      });

      const results = await Promise.all(promises);

      results.forEach((result) => {
        if (result.success) {
          successful++;
        } else {
          failed++;
        }
      });
    }

    return { successful, failed };
  }

  /**
   * Fallback method to update orders individually if bulk update fails
   */
  private async fallbackToIndividualUpdates(
    orders: SalesOrder[],
  ): Promise<{ processed: number; failed: number }> {
    logger.info(
      `Falling back to individual updates for ${orders.length} orders`,
    );

    let processed = 0;
    let failed = 0;

    for (const order of orders) {
      try {
        await this.updateOrderToPaymentFailed(order);
        processed++;
      } catch (error) {
        failed++;
        logger.error(
          `Failed to update order ${order.increment_id} individually:`,
          error,
        );
      }
    }

    return { processed, failed };
  }

  /**
   * Update a single order status to PAYMENT_FAILED and trigger notifications
   * Includes analytics tracking for system-initiated failures
   * Used as fallback when bulk operations fail
   */
  private async updateOrderToPaymentFailed(order: SalesOrder): Promise<void> {
    const startTime = Date.now();
    const hoursOld = Math.floor(
      (Date.now() - new Date(order.createdAt).getTime()) / (1000 * 60 * 60),
    );

    try {
      logger.info(
        `Starting payment failure update for order ${order.increment_id}`,
        {
          order_id: order.increment_id,
          customer_id: order.customer_id,
          grand_total: order.grand_total,
          hours_old: hoursOld,
          analytics_key: 'system_initiated_payment_failure',
          initiated_by: 'system_scheduler',
        },
      );

      // Update the order status in the database with analytics metadata
      const updateData: any = {
        status: OrderStatuses.PAYMENT_FAILED,
        failed_by: 'system_scheduler',
        failure_timestamp: new Date(),
      };

      const updateResult = await SalesOrder.update(updateData, {
        where: { order_id: order.order_id },
        returning: true, // Get updated record for verification
      });

      if (updateResult[0] === 0) {
        throw new Error(
          `Failed to update order ${order.increment_id} - no rows affected`,
        );
      }

      // Update the order object for notification
      order.status = OrderStatuses.PAYMENT_FAILED;

      // Trigger ERP notification and other side effects
      await this.notifyERP.notifyStatusUpdate(
        order,
        order.items,
        order.address,
        order.payment,
        null,
        order.sales_order_extra_info,
        false,
      );

      const duration = Date.now() - startTime;

      // Log successful update with analytics data
      logger.info(
        `Order ${order.increment_id} successfully updated to payment_failed`,
        {
          order_id: order.increment_id,
          customer_id: order.customer_id,
          customer_email: order.customer_email,
          grand_total: order.grand_total,
          currency: order.order_currency_symbol,
          hours_old: hoursOld,
          processing_duration_ms: duration,
          analytics_key: 'system_payment_failure_completed',
          initiated_by: 'system_scheduler',
          notifications_sent: true,
          timestamp: new Date().toISOString(),
        },
      );
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log error with analytics data
      logger.error(
        `Error updating order ${order.increment_id} to payment_failed`,
        {
          order_id: order.increment_id,
          customer_id: order.customer_id,
          grand_total: order.grand_total,
          hours_old: hoursOld,
          processing_duration_ms: duration,
          analytics_key: 'system_payment_failure_error',
          initiated_by: 'system_scheduler',
          error_message: error.message,
          error_stack: error.stack,
          timestamp: new Date().toISOString(),
        },
      );

      throw error;
    }
  }

  /**
   * Manual trigger method for testing or emergency use
   * This method can be called via API endpoint if needed
   */
  async manualPaymentTimeoutCheck(): Promise<{
    processed: number;
    failed: number;
    message: string;
  }> {
    logger.info('Manual payment timeout check triggered');

    try {
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - 24);

      const expiredOrders = await this.findExpiredPaymentPendingOrders(
        cutoffTime,
      );

      if (expiredOrders.length === 0) {
        return {
          processed: 0,
          failed: 0,
          message: 'No expired payment pending orders found',
        };
      }

      let totalProcessed = 0;
      let totalFailed = 0;

      const batchSize = 50;
      for (let i = 0; i < expiredOrders.length; i += batchSize) {
        const batch = expiredOrders.slice(i, i + batchSize);
        const batchResults = await this.processBatch(batch);

        totalProcessed += batchResults.processed;
        totalFailed += batchResults.failed;
      }

      return {
        processed: totalProcessed,
        failed: totalFailed,
        message: `Manual payment timeout check completed. Found ${expiredOrders.length} expired orders.`,
      };
    } catch (error) {
      logger.error('Error in manual payment timeout check:', error);
      throw error;
    }
  }

  /**
   * Get statistics about payment pending orders
   */
  async getPaymentPendingStats(): Promise<{
    totalPaymentPending: number;
    expiredPaymentPending: number;
    recentPaymentPending: number;
  }> {
    try {
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - 24);

      const [totalPaymentPending, expiredPaymentPending] = await Promise.all([
        SalesOrder.count({
          where: { status: OrderStatuses.PAYMENT_PENDING },
        }),
        SalesOrder.count({
          where: {
            status: OrderStatuses.PAYMENT_PENDING,
            createdAt: { [Op.lte]: cutoffTime }, // Use lte for >= 24 hours condition
          },
        }),
      ]);

      const recentPaymentPending = totalPaymentPending - expiredPaymentPending;

      return {
        totalPaymentPending,
        expiredPaymentPending,
        recentPaymentPending,
      };
    } catch (error) {
      logger.error('Error getting payment pending stats:', error);
      throw error;
    }
  }
}
