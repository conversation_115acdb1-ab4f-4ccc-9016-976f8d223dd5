/**
 * Possible user-types for place-order Action
 */
export enum UserTypes {
  GUEST = 'guest',
  REGISTERED = 'registered',
}

/**
 * Possible address-types associated with an order
 */
export enum AddressTypes {
  BILLING = 'billing',
  SHIPPING = 'shipping',
}

/**
 * Some available payment methods
 */
export enum PaymentMethods {
  RAZOR_PAY = 'razorpay',
  COD = 'cashondelivery',
}

/**
 * Some possible order statuses
 */
export const OrderStatuses = {
  NEW_ORDER: 'new_order',
  HOLDED: 'holded',
  EXCLUDE: 'exclude',
  PAYMENT_LINK_SENT: 'payment_link_sent',
  NOT_PAID: 'not_paid',
  PAYMENT_RECEIVED: 'payment_received',
  PAYMENT_AUTHORIZE: 'payment_authorize',
  ORDER_PROCESSED: 'order_processed',
  AUTO_INVOICED: 'auto_invoiced',
  CLOSED: 'closed',
  COMPLETE: 'complete',
  // status received from ERP
  DELIVERY_EXCEPTION: 'delivery_exception',
  PAYMENT_PENDING: 'payment_pending',
  PAYMENT_FAILED: 'payment_failed',
  DELIVERED: 'delivered',
  PROCESSING: 'processing',
  ORDER_CONFIRMED: 'order_confirmed',
  CANCELLED: 'cancelled',
  PACKED: 'packed',
  SHIPPED: 'shipped',
  RETURNED: 'returned',
  FAILED: 'failed',
};

/**
 * Possible cases in which an order is eligible to cancel
 */
export const CancellationAvailableStatuses = [
  ...OrderStatuses.PAYMENT_PENDING,
  ...OrderStatuses.NEW_ORDER,
  ...OrderStatuses.PAYMENT_LINK_SENT,
  ...OrderStatuses.NOT_PAID,
  ...OrderStatuses.PAYMENT_RECEIVED,
  ...OrderStatuses.ORDER_PROCESSED,
];

/**
 * Possible cases in which an order is eligible to return
 */
export const ReturnAvailableStatuses = [
  ...OrderStatuses.DELIVERY_EXCEPTION,
  ...OrderStatuses.DELIVERED,
];

/**
 * Possible status to send in fetchOrder response
 */
export enum FetchOrderStatuses {
  SUCCESS = 'success',
  PROCESSING = 'processing',
  FAILED = 'failed',
}

/**
 * Possible item-status to be sent to ERP System webhook
 */
export enum ERPItemStatuses {
  PENDING = 'STATUS_PENDING',
  INVOICED = ' STATUS_INVOICED',
}

/**
 * Reward points rate multiplication factor
 */

export const REWARD_POINT_RATE_MULTIPLIER = 2;

/**
 * Possible razorpay payment statuses
 */
export enum RazorpayPaymentStatuses {
  CAPTURED = 'captured',
  FAILED = 'failed',
  AUTHORIZED = 'authorized',
}

/**
 * NO of months back cusotmer order fetch for item review
 */

export const NO_OF_MONTHS_BACK = 6;

/**
 * country code letterwise to encode country_id
 */
export const countryAsciCode = {
  a: '01',
  b: '02',
  c: '03',
  d: '04',
  e: '05',
  f: '06',
  g: '07',
  h: '08',
  i: '09',
  j: '10',
  k: '11',
  l: '12',
  m: '13',
  n: '14',
  o: '15',
  p: '16',
  q: '17',
  r: '18',
  s: '19',
  t: '20',
  u: '21',
  v: '22',
  w: '23',
  x: '24',
  y: '25',
  z: '26',
};

/**
 * consimer queue options
 */

export const CANCEL_ORDER_QUEUE_OPTIONS = {
  EXCHANGE_NAME: 'cancel_order',
  QUEUE: 'cancel_order',
  EXCHANGE_TYPE: 'topic',
  ROUTING_KEY: 'cancel_order.requested',
};

export const ORDER_PUBLISH_SOURCE = 'dentalkart.order.service';

export const NOTIFICATION_EVENTS = {
  new_order: 'cod_order',
  payment_received: 'payment_received',
  payment_failed: 'payment_failed',
  failed: 'failed',
  cancelled: 'cancelled',
};

export const ORDER_VALIDADTION_ERROR = {
  invalid_grand_total: `Invalid order: Grand Total must be greater than zero`,
  invalid_subtotal:
    'Invalid order: Items total amount must be greater than zero',
};

export const EVENT_THRESH_HOLD_DATE = new Date('2025-02-18T00:00:00Z');
