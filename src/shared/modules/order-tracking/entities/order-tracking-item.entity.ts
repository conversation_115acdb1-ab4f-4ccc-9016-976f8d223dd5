import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrderTracking } from './order-tracking.entity';

@Entity()
export class OrderTrackingItem {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'varchar', length: 50, nullable: false })
  order_id!: string;

  @Column({ type: 'varchar', length: 50 })
  sku!: string;

  @Column({ type: 'int' })
  product_id!: number;

  @Column({ type: 'int' })
  ordered_qty!: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  name?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  url_key?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  image?: string;

  @CreateDateColumn({ type: 'datetime' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at!: Date;

  @Column('json', { nullable: true })
  item_extra_info?: Record<string, any>;

  @Column({ type: 'boolean', default: false })
  is_reviewed!: boolean;

  @ManyToOne(
    () => OrderTracking,
    (orderTracking) => orderTracking.orderTrackingItem,
  )
  @JoinColumn({ name: 'order_id', referencedColumnName: 'orderId' })
  orderTracking?: OrderTracking;
}
