import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { OrderStatus } from '../enums/order-status.enum';
import { VinculumShipmentStatus } from '../../vinculum-shipment-status/entities/vinculum-shipment-status.entity';
import { OrderTrackingItem } from './order-tracking-item.entity';

@Entity()
@Index('IDX_UNIQUE_ORDER_ID', ['orderId'], {
  unique: true,
  where: 'orderId IS NOT NULL',
})
export class OrderTracking {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'varchar', length: 50, nullable: false })
  orderId!: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  customerId!: string;

  @Column({ type: 'datetime', nullable: false })
  orderDate!: Date;

  @Column({ type: 'smallint', unsigned: true, nullable: false })
  lineItemCount!: number;

  @Column({ type: 'smallint', unsigned: true, nullable: true })
  expDispatchDays: number;

  @Column({ type: 'smallint', unsigned: true, nullable: true })
  expDeliveryDays: number;

  @Column({ type: 'datetime', nullable: true })
  createAtStoreDate: Date;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.ORDER_RECEIVED,
  })
  latestStatus!: OrderStatus;

  @Column({ type: 'char', length: 6, nullable: false })
  wmsVendor!: string;

  @Column({ type: 'char', length: 6, nullable: false })
  logisticsVendor!: string;

  @CreateDateColumn({ type: 'datetime' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updatedAt!: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  grandTotal: number;

  @Column('json', { nullable: true })
  deliveredAt?: any;

  @Column('json', { nullable: true })
  orderExtraInfo?: Record<string, any>;

  @OneToMany(
    () => VinculumShipmentStatus,
    (vinculumShipmentStatus) => vinculumShipmentStatus.orderTracking,
  )
  vinculumShipmentStatuses?: VinculumShipmentStatus[];

  @OneToMany(
    () => OrderTrackingItem,
    (OrderTrackingItem) => OrderTrackingItem.orderTracking,
    { cascade: true },
  )
  orderTrackingItem?: OrderTrackingItem[];
}
