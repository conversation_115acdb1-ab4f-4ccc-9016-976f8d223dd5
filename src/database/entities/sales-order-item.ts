import { B<PERSON>IN<PERSON>, STRING, B<PERSON>OLEAN, INTEGER, DECIMAL } from 'sequelize';
import {
  Column,
  ForeignKey,
  HasOne,
  HasMany,
  Model,
  Table,
} from 'sequelize-typescript';
import { SalesOrder } from './sales-order';
import { SalesOrderTaxItem } from './sales-order-tax-item';
import { OrderItemExtraInfo } from './order-item-extra-info';
import { SalesOrderItemPromotion } from './sales-order-item-promotion';

@Table({ tableName: 'sales_order_item', paranoid: true, timestamps: true })
export class SalesOrderItem extends Model {
  @Column({ type: INTEGER, autoIncrement: true, primaryKey: true })
  order_item_id: number;
  @ForeignKey(() => SalesOrder)
  @Column({ type: INTEGER })
  order_id: number;
  @Column({ type: INTEGER })
  parent_item_id: number;
  @Column({ type: INTEGER })
  quote_item_id: number;
  @Column({ type: INTEGER })
  store_id: number;
  @Column({ type: INTEGER })
  product_id: number;
  @Column({ type: STRING })
  product_sku: string;
  @Column({ type: STRING })
  product_name: string;
  @Column({ type: STRING })
  product_type: string;
  @Column({ type: STRING })
  product_options: string;
  @Column({ type: DECIMAL(20, 4) })
  weight: number;
  @Column({ type: BOOLEAN })
  is_virtual: boolean;
  @Column({ type: STRING })
  applied_rule_ids: string;
  @Column({ type: DECIMAL(20, 2) })
  qty_backordered: number;
  @Column({ type: DECIMAL(20, 2) })
  qty_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  qty_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  qty_ordered: number;
  @Column({ type: DECIMAL(20, 2) })
  qty_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  qty_shipped: number;
  @Column({ type: DECIMAL(20, 2) })
  price: number;
  @Column({ type: DECIMAL(20, 2) })
  base_price: number;
  @Column({ type: DECIMAL(20, 2) })
  original_price: number;
  @Column({ type: DECIMAL(20, 2) })
  base_original_price: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_percent: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_percent: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  amount_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_amount_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  row_total: number;
  @Column({ type: DECIMAL(20, 2) })
  base_row_total: number;
  @Column({ type: DECIMAL(20, 2) })
  row_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_row_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  row_weight: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_before_discount: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_before_discount: number;
  @Column({ type: DECIMAL(20, 2) })
  price_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  base_price_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  row_total_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  base_row_total_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_tax_compensation_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_tax_compensation_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_tax_compensation_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_tax_compensation_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_tax_compensation_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_tax_compensation_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_tax_compensation_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  free_shipping: number;
  @Column({ type: STRING })
  thumbnail_url: string;
  @Column({ type: STRING })
  url_key: string;
  @Column({ type: BIGINT })
  reward_points: number;
  @Column({ type: STRING })
  product_expiry_date: string;
  @Column({ type: DECIMAL(20, 2) })
  item_handling_fee: number;

  @HasOne(() => SalesOrderTaxItem)
  tax: SalesOrderTaxItem;

  @HasOne(() => OrderItemExtraInfo)
  itemExtraInfo: OrderItemExtraInfo;

  @HasMany(() => SalesOrderItemPromotion, { onDelete: 'CASCADE', hooks: true })
  salesOrderItemPromotions: SalesOrderItemPromotion[];
}
