import { STRING, BOOLEAN, INTEGER, DO<PERSON><PERSON><PERSON>, TEXT } from 'sequelize';
import { Column, ForeignKey, Model, Table } from 'sequelize-typescript';
import { SalesOrder } from './sales-order';

@Table({ tableName: 'sales_order_address', paranoid: true, timestamps: true })
export class <PERSON><PERSON>rderAddress extends Model {
  @Column({ type: INTEGER, autoIncrement: true, primaryKey: true })
  order_address_id: number;
  @ForeignKey(() => SalesOrder)
  @Column({ type: INTEGER })
  order_id: number;
  @Column({ type: INTEGER })
  customer_address_id: number;
  @Column({ type: INTEGER })
  quote_address_id: number;
  @Column({ type: INTEGER })
  region_id: number;
  @Column({ type: INTEGER })
  customer_id: number;
  @Column({ type: STRING })
  region: string;
  @Column({ type: STRING })
  postcode: string;
  @Column({ type: DOUBLE, allowNull: true })
  latitude: number;
  @Column({ type: DOUBLE, allowNull: true })
  longitude: number;
  @Column({ type: STRING, allowNull: true })
  tag: string;
  @Column({ type: TEXT, allowNull: true })
  map_address: string;
  @Column({ type: TEXT, allowNull: true })
  customer_street_2: string;
  @Column({ type: STRING })
  lastname: string;
  @Column({ type: STRING })
  street: string;
  @Column({ type: STRING })
  city: string;
  @Column({ type: STRING })
  email: string;
  @Column({ type: STRING })
  telephone: string;
  @Column({ type: STRING, allowNull: false })
  country_id: string;
  @Column({ type: STRING })
  firstname: string;
  @Column({ type: STRING })
  address_type: string;
  @Column({ type: STRING })
  company: string;
  @Column({ type: STRING })
  gst_id: string;
  @Column({ type: BOOLEAN })
  gst_is_valid: boolean;
}
