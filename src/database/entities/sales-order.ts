import { STRING, B<PERSON><PERSON>EA<PERSON>, INTEGER, DECIMAL, DATE } from 'sequelize';
import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>O<PERSON>, Model, Table } from 'sequelize-typescript';
import { SalesOrderAddress } from './sales-order-address';
import { SalesOrderItem } from './sales-order-item';
import { SalesOrderPayment } from './sales-order-payment';
import { SalesOrderTax } from './sales-order-tax';
import { SalesOrderExtraInfo } from './sales-order-extra-info';
import { SalesOrderAmountPromotion } from './sales-order-amount-promotion';

@Table({
  tableName: 'sales_order',
  paranoid: true,
  timestamps: true,
  initialAutoIncrement: '3000001',
})
export class SalesOrder extends Model {
  @Column({ autoIncrement: true, primaryKey: true, type: INTEGER })
  order_id: number;
  @Column({ type: STRING, allowNull: false })
  status: string;
  @Column({ type: STRING, allowNull: true })
  coupon_code: string;
  @Column({ type: STRING, allowNull: true })
  protect_code: string;
  @Column({ type: STRING, allowNull: true })
  shipping_description: string;
  @Column({ type: BOOLEAN, defaultValue: false })
  is_virtual: boolean;
  @Column({ type: INTEGER })
  store_id: number;
  @Column({ type: INTEGER })
  customer_id: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_grand_total: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_tax_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_tax_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_subtotal: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_subtotal_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  base_subtotal_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_subtotal_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_tax_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_to_global_rate: number;
  @Column({ type: DECIMAL(20, 2) })
  base_to_order_rate: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_invoiced_cost: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_offline_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_online_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_paid: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_qty_ordered: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  grand_total: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_tax_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_tax_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  subtotal: number;
  @Column({ type: DECIMAL(20, 2) })
  subtotal_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  subtotal_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  subtotal_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  tax_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  total_canceled: number;
  @Column({ type: DECIMAL(20, 2) })
  total_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  total_offline_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  total_online_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  total_paid: number;
  @Column({ type: DECIMAL(20, 2) })
  total_qty_ordered: number;
  @Column({ type: DECIMAL(20, 2) })
  total_refunded: number;
  @Column({ type: BOOLEAN, defaultValue: false })
  customer_is_guest: boolean;
  @Column({ type: INTEGER })
  billing_address_id: number;
  @Column({ type: INTEGER })
  customer_group_id: number;
  @Column({ type: INTEGER })
  edit_increment: number;
  @Column({ type: DECIMAL(20, 2) })
  payment_auth_expiration: number;
  @Column({ type: INTEGER })
  quote_address_id: number;
  @Column({ type: INTEGER, unique: true })
  quote_id: number;
  @Column({ type: INTEGER })
  shipping_address_id: number;
  @Column({ type: DECIMAL(20, 2) })
  adjustment_negative: number;
  @Column({ type: DECIMAL(20, 2) })
  adjustment_positive: number;
  @Column({ type: DECIMAL(20, 2) })
  base_adjustment_negative: number;
  @Column({ type: DECIMAL(20, 2) })
  base_adjustment_positive: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_discount_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_subtotal_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  base_total_due: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_discount_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  subtotal_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  total_due: number;
  @Column({ type: DECIMAL(20, 2) })
  weight: number;
  @Column({ type: STRING, unique: true })
  increment_id: string;
  @Column({ type: STRING })
  applied_rule_ids: string;
  @Column({ type: STRING })
  base_currency_code: string;
  @Column({ type: STRING })
  customer_email: string;
  @Column({ type: STRING })
  customer_firstname: string;
  @Column({ type: STRING })
  customer_lastname: string;
  @Column({ type: STRING })
  discount_description: string;
  @Column({ type: STRING })
  global_currency_code: string;
  @Column({ type: STRING })
  hold_before_state: string;
  @Column({ type: STRING })
  hold_before_status: string;
  @Column({ type: STRING })
  order_currency_code: string;
  @Column({ type: STRING })
  original_increment_id: string;
  @Column({ type: STRING })
  relation_child_id: string;
  @Column({ type: STRING })
  relation_child_real_id: string;
  @Column({ type: STRING })
  relation_parent_id: string;
  @Column({ type: STRING })
  relation_parent_real_id: string;
  @Column({ type: STRING })
  remote_ip: string;
  @Column({ type: STRING })
  shipping_method: string;
  @Column({ type: STRING })
  store_currency_code: string;
  @Column({ type: STRING })
  store_name: string;
  @Column({ type: STRING })
  customer_note: string;
  @Column({ type: INTEGER })
  total_item_count: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_tax_compensation_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_tax_compensation_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_discount_tax_compensation_amount: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_discount_tax_compensation_amnt: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_tax_compensation_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_tax_compensation_invoiced: number;
  @Column({ type: DECIMAL(20, 2) })
  discount_tax_compensation_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  base_discount_tax_compensation_refunded: number;
  @Column({ type: DECIMAL(20, 2) })
  shipping_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  base_shipping_incl_tax: number;
  @Column({ type: DECIMAL(20, 2) })
  rewards_discount: number;
  @Column({ type: STRING(10), allowNull: false })
  order_currency_symbol: string;
  @Column({ type: DECIMAL(20, 2) })
  handling_fee: number;
  @Column({ type: STRING })
  platform: string;
  @Column({ type: STRING })
  app_version: string;
  @Column({ type: BOOLEAN, defaultValue: false })
  is_active_membership: boolean;
  @Column({ type: STRING, allowNull: true })
  failed_by: string;
  @Column({ type: DATE, allowNull: true })
  failure_timestamp: Date;

  @HasMany(() => SalesOrderItem)
  items: SalesOrderItem[];

  @HasMany(() => SalesOrderAddress)
  address: SalesOrderAddress[];

  @HasOne(() => SalesOrderPayment)
  payment: SalesOrderPayment;

  @HasOne(() => SalesOrderTax)
  tax: SalesOrderTax;

  @HasOne(() => SalesOrderExtraInfo)
  sales_order_extra_info: SalesOrderExtraInfo;

  @HasMany(() => SalesOrderAmountPromotion, { onDelete: 'CASCADE' })
  salesOrderAmountPromotions: SalesOrderAmountPromotion[];
}
