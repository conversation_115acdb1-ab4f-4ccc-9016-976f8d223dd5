import { STRING, INTEGER, DECIMAL } from 'sequelize';
import { Column, ForeignKey, Model, Table } from 'sequelize-typescript';
import { SalesOrderItem } from './sales-order-item';
import { SalesOrderTax } from './sales-order-tax';

@Table({ tableName: 'sales_order_tax_item', paranoid: true, timestamps: true })
export class SalesOrderTaxItem extends Model {
  @Column({ type: INTEGER, autoIncrement: true, primaryKey: true })
  tax_item_id: number;
  @ForeignKey(() => SalesOrderTax)
  @Column({ type: INTEGER })
  tax_id: number;
  @ForeignKey(() => SalesOrderItem)
  @Column({ type: INTEGER })
  item_id: number;
  @Column({ type: DECIMAL(20, 4) })
  tax_percent: number;
  @Column({ type: DECIMAL(20, 4) })
  amount: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount: number;
  @Column({ type: DECIMAL(20, 4) })
  real_amount: number;
  @Column({ type: DECIMAL(20, 4) })
  real_base_amount: number;
  @Column({ type: STRING(32) })
  taxable_item_type: string;
}
