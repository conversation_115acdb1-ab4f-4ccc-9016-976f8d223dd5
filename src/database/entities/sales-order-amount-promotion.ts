import { BIGIN<PERSON>, INTEGER, STRING, JSON, DAT<PERSON> } from 'sequelize';
import {
  Column,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { SalesOrder } from './sales-order';

@Table({
  tableName: 'sales_order_amount_promotion',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class SalesOrderAmountPromotion extends Model {
  @Column({ type: BIGINT, autoIncrement: true, primaryKey: true })
  id: number;

  @ForeignKey(() => SalesOrder)
  @Column({ type: INTEGER, allowNull: false })
  order_id: number;

  @Column({ type: BIGINT, allowNull: false })
  promotion_id: number;

  @Column({ type: STRING, allowNull: false })
  sku: string;

  @Column({ type: INTEGER, allowNull: false })
  qty: number;

  @Column({ type: JSON, allowNull: false })
  meta_info: Record<string, any>;

  @Column({ type: DATE, allowNull: false })
  created_at: Date;

  @Column({ type: DATE, allowNull: false })
  updated_at: Date;

  @BelongsTo(() => SalesOrder, 'order_id')
  order: SalesOrder;
}
