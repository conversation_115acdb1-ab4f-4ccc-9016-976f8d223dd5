import { STRING, INTEGER, DATE } from 'sequelize';
import {
  Table,
  Model,
  Column,
  ForeignKey,
  DataType,
} from 'sequelize-typescript';
import { SalesOrder } from './sales-order';
import { BOOLEAN } from 'sequelize';

@Table({
  tableName: 'sales_order_extra_info',
  paranoid: true,
  timestamps: true,
})
export class SalesOrderExtraInfo extends Model {
  @Column({ type: INTEGER, autoIncrement: true, primaryKey: true })
  extra_info_entity_id: number;
  @ForeignKey(() => SalesOrder)
  @Column({ type: INTEGER })
  order_id: number;
  @Column({ type: STRING(50) })
  registration_no: string;
  @Column({ type: INTEGER })
  exp_delivery_days: number;
  @Column({ type: INTEGER })
  exp_dispatch_days: number;
  @Column({ type: DATE, allowNull: true })
  processed_at: Date;
  @Column({
    type: DataType.JSON,
    defaultValue: [],
  })
  events_occurred: string[];
  @Column({ type: STRING(10), allowNull: true })
  max_delivery_warehouse_code: string;
}
