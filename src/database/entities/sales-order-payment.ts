import { DECIMA<PERSON>, TEXT, STRING, INTEGER, JSO<PERSON> } from 'sequelize';
import { Column, ForeignKey, Model, Table } from 'sequelize-typescript';
import { SalesOrder } from './sales-order';

@Table({ tableName: 'sales_order_payment', paranoid: true, timestamps: true })
export class SalesOrderPayment extends Model {
  @Column({ type: INTEGER, autoIncrement: true, primaryKey: true })
  payment_id: number;
  @ForeignKey(() => SalesOrder)
  @Column({ type: INTEGER })
  order_id: number;
  @Column({ type: DECIMAL(20, 4) })
  base_shipping_captured: number;
  @Column({ type: DECIMAL(20, 4) })
  shipping_captured: number;
  @Column({ type: DECIMAL(20, 4) })
  amount_refunded: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount_paid: number;
  @Column({ type: DECIMAL(20, 4) })
  amount_canceled: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount_authorized: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount_paid_online: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount_refunded_online: number;
  @Column({ type: DECIMAL(20, 4) })
  base_shipping_amount: number;
  @Column({ type: DECIMAL(20, 4) })
  shipping_amount: number;
  @Column({ type: DECIMAL(20, 4) })
  amount_paid: number;
  @Column({ type: DECIMAL(20, 4) })
  amount_authorized: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount_ordered: number;
  @Column({ type: DECIMAL(20, 4) })
  base_shipping_refunded: number;
  @Column({ type: DECIMAL(20, 4) })
  shipping_refunded: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount_refunded: number;
  @Column({ type: DECIMAL(20, 4) })
  amount_ordered: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount_canceled: number;
  @Column({ type: STRING })
  method: string;
  @Column({ type: JSON })
  additional_information: object;
  @Column({ type: STRING })
  webhook_secret: string;
  @Column({ type: STRING })
  order_receipt_id: string;
  @Column({ type: STRING })
  razorpay_order_id: string;
  @Column({ type: JSON })
  acquirer_data: object;
  @Column({ type: JSON })
  notes: object;
  @Column({ type: STRING })
  razorpay_event_id_authorize: string;
  @Column({ type: STRING })
  razorpay_event_id_capture: string;
  @Column({ type: STRING })
  razorpay_event_id_failed: string;
  @Column({ type: STRING })
  razorpay_payment_id: string;
}
