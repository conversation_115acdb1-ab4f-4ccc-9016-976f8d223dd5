import { DECIMAL, STRING, INTEGER } from 'sequelize';
import {
  Table,
  Model,
  Column,
  ForeignKey,
  HasMany,
} from 'sequelize-typescript';
import { SalesOrder } from './sales-order';
import { SalesOrderTaxItem } from './sales-order-tax-item';

@Table({ tableName: 'sales_order_tax', paranoid: true, timestamps: true })
export class SalesOrderTax extends Model {
  @Column({ type: INTEGER, autoIncrement: true, primaryKey: true })
  tax_id: number;
  @ForeignKey(() => SalesOrder)
  @Column({ type: INTEGER })
  order_id: number;
  @Column({ type: STRING(30) })
  code: string;
  @Column({ type: STRING(30) })
  title: string;
  @Column({ type: DECIMAL(20, 4) })
  percent: number;
  @Column({ type: DECIMAL(20, 4) })
  amount: number;
  @Column({ type: DECIMAL(20, 4) })
  base_amount: number;
  @Column({ type: DECIMAL(20, 4) })
  base_real_amount: number;

  @HasMany(() => SalesOrderTaxItem)
  items: [SalesOrderTaxItem];
}
