import { logger } from '../../utils/service-logger';
import { SalesOrderStatusState } from '../entities/sales-order-status-state';
import { orderStatuses } from './data/order-statuses';

export class OrderStatusSeeder {
  /**
   * It seeds default static values for status & state
   */
  seedOrderStatuses = async () => {
    try {
      await SalesOrderStatusState.bulkCreate(orderStatuses);
      logger.info('Status-State seeded successfully!!!');
    } catch (error) {
      logger.error('Order status seeding error', error);
    }
  };
}
