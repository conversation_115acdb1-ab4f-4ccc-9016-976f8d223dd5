/**
 * All Order related statuses are defined here
 */
export const orderStatuses = [
  { status: 'holded', state: 'holded' },
  { status: 'exclude', state: 'exclude' },
  { status: 'payment_pending', state: 'payment_pending' },
  { status: 'new_order', state: 'new_order' },
  { status: 'payment_link_sent', state: 'payment_link_sent' },
  { status: 'not_paid', state: 'not_paid' },
  { status: 'payment_received', state: 'payment_received' },
  { status: 'payment_authorize', state: 'payment_authorize' },
  { status: 'order_processed', state: 'order_processed' },
  { status: 'auto_invoiced', state: 'auto_invoiced' },
  { status: 'canceled', state: 'canceled' },
  { status: 'closed', state: 'closed' },
  { status: 'complete', state: 'complete' },
  { status: 'delivery_exception', state: 'delivery_exception' },
  { status: 'delivered', state: 'delivered' },
  { status: 'processing', state: 'processing' },
];
