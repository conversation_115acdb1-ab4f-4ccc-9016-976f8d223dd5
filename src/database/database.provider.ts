import { Sequelize } from 'sequelize-typescript';
import config from '../config/env';
import { SalesOrder } from './entities/sales-order';
import { SalesOrderAddress } from './entities/sales-order-address';
import { SalesOrderItem } from './entities/sales-order-item';
import { SalesOrderPayment } from './entities/sales-order-payment';
import { SalesOrderStatusState } from './entities/sales-order-status-state';
import { SalesOrderTax } from './entities/sales-order-tax';
import { SalesOrderTaxItem } from './entities/sales-order-tax-item';
import { SalesOrderExtraInfo } from './entities/sales-order-extra-info';
import { OrderItemExtraInfo } from './entities/order-item-extra-info';
import { SalesOrderAmountPromotion } from './entities/sales-order-amount-promotion';
import { SalesOrderItemPromotion } from './entities/sales-order-item-promotion';

export const databaseProviders = [
  {
    provide: 'SEQUELIZE',
    useFactory: async () => {
      const sequelize = new Sequelize({
        ...config.database,
        dialect: 'mysql',
        logging: false,
        models: [
          SalesOrder,
          SalesOrderItem,
          SalesOrderAddress,
          SalesOrderPayment,
          SalesOrderTax,
          SalesOrderTaxItem,
          SalesOrderStatusState,
          SalesOrderExtraInfo,
          OrderItemExtraInfo,
          SalesOrderAmountPromotion,
          SalesOrderItemPromotion,
        ],
      });
      await sequelize.sync();
      return sequelize;
    },
  },
];
