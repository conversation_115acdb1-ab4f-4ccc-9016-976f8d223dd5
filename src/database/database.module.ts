import { Module } from '@nestjs/common';
import { databaseProviders } from './database.provider';
import { OrderStatusSeeder } from './seeder/order-status-states';

@Module({
  providers: [...databaseProviders],
  exports: [...databaseProviders],
})
export class DatabaseModule {
  constructor() {
    // uncomment below line to seed order status-state
    // new OrderStatusSeeder().seedOrderStatuses();
  }
}
