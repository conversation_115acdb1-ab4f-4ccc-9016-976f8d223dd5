import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.addColumn('order_item_extra_info', 'delivery_info', {
    type: DataTypes.JSON,
    allowNull: true,
    comment:
      'JSON column to store delivery information including days, dispatch_days, and warehouse_code',
  });
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.removeColumn('order_item_extra_info', 'delivery_info');
};
