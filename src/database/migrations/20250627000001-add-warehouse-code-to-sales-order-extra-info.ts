import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.addColumn(
    'sales_order_extra_info',
    'max_delivery_warehouse_code',
    {
      type: DataTypes.STRING(10),
      allowNull: true,
      comment: 'Warehouse code for maximum delivery days',
    },
  );
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.removeColumn(
    'sales_order_extra_info',
    'max_delivery_warehouse_code',
  );
};
