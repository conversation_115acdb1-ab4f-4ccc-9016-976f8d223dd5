'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */

    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.addColumn(
        'sales_order',
        'platform',
        {
          type: Sequelize.DataTypes.STRING,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'sales_order',
        'app_version',
        {
          type: Sequelize.DataTypes.STRING,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'sales_order',
        'handling_fee',
        {
          type: Sequelize.DataTypes.DECIMAL(20, 2),
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'sales_order_item',
        'item_handling_fee',
        {
          type: Sequelize.DataTypes.DECIMAL(20, 2),
        },
        { transaction },
      );
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('sales_order', 'platform', {
        transaction,
      });
      await queryInterface.removeColumn('sales_order', 'app_version', {
        transaction,
      });
      await queryInterface.removeColumn('sales_order', 'handling_fee', {
        transaction,
      });
      await queryInterface.removeColumn(
        'sales_order_item',
        'item_handling_fee',
        {
          transaction,
        },
      );
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
