'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.changeColumn(
        'sales_order_address',
        'region_id',
        {
          type: Sequelize.DataTypes.INTEGER,
          allowNull: true,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'region',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'postcode',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'street',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'city',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'address_type',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        },
        {
          transaction,
        },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw err;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.changeColumn(
        'sales_order_address',
        'region_id',
        {
          type: Sequelize.DataTypes.INTEGER,
          allowNull: false,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'region',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: false,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'postcode',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: false,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'street',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: false,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'city',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: false,
        },
        {
          transaction,
        },
      );

      await queryInterface.changeColumn(
        'sales_order_address',
        'address_type',
        {
          type: Sequelize.DataTypes.STRING,
          allowNull: false,
        },
        {
          transaction,
        },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw err;
    }
  },
};
