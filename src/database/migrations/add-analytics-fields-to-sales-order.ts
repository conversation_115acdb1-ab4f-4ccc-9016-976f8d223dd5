'use strict';

/** @type {import('sequelize-cli').Migration} */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.addColumn(
        'sales_order',
        'failed_by',
        {
          type: Sequelize.STRING(50),
          allowNull: true,
          comment:
            'Who/what initiated the failure (e.g., system_scheduler, admin_user, customer)',
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'sales_order',
        'failure_timestamp',
        {
          type: Sequelize.DATE,
          allowNull: true,
          comment: 'Timestamp when the order was marked as failed',
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.removeColumn('sales_order', 'failure_timestamp', {
        transaction,
      });
      await queryInterface.removeColumn('sales_order', 'failed_by', {
        transaction,
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
