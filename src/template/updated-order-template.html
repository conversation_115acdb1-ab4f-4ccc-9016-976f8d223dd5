<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
      }
      .email-container {
        background-color: #ffffff;
        max-width: 700px;
        margin: 20px auto;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      .header {
        background-color: #2a3e4c;
        text-align: center;
        padding: 20px 0;
      }
      .header img {
        max-width: 180px;
      }
      .content {
        padding: 20px;
        color: #2a3e4c;
      }
      .content h2 {
        font-size: 22px;
        margin-bottom: 10px;
      }
      .order-info {
        background-color: #f7f7f7;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .order-info h3 {
        margin: 0;
        font-size: 16px;
        color: #333333;
      }
      .order-info p {
        margin: 0;
        font-size: 14px;
        color: #555555;
      }
      .order-details,
      .address-section {
        width: 100%;
        margin-bottom: 20px;
      }
      .order-details th,
      .order-details td,
      .address-table th,
      .address-table td {
        padding: 10px;
        border-bottom: 1px solid #dddddd;
        text-align: left;
      }
      .order-details th,
      .address-table th {
        background-color: #f7f7f7;
        color: #2a3e4c;
      }
      .order-details td {
        text-align: right;
      }
      .order-details td.product {
        text-align: left;
      }
      .address-section {
        display: flex;
        margin-top: 20px;
      }
      .address-section .column {
        width: 48%;
        background-color: #f4f8fb;
        padding: 15px;
      }
      .column h3 {
        color: #0066cc;
        margin-bottom: 10px;
        font-size: 16px;
      }
      .column p {
        margin: 0;
        line-height: 1.4;
        color: #555555;
      }
      .total-amount,
      .price-distribution {
        background-color: #f7f7f7;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
      }
      .total-amount p,
      .price-distribution p {
        margin: 0;
        line-height: 1.6;
      }
      .total-amount p span,
      .price-distribution p span {
        font-weight: bold;
        color: #2a3e4c;
      }

      .price-detail {
        display: flex;
        margin-bottom: 5px;
      }
      .rewards {
        background-color: #e0f7fa;
        padding: 15px;
        border-radius: 5px;
        text-align: center;
        margin-bottom: 20px;
      }
      .rewards p {
        margin: 0;
        font-weight: bold;
        color: #00796b;
      }
      .button {
        background-color: #0066cc;
        color: white !important;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 5px;
        display: inline-block;
        margin-top: 20px;
      }
      .button-container {
        text-align: right;
        margin-bottom: 15px;
      }
      .footer {
        background-color: #eff5fc;
        color: #1d2b36;
        text-align: center;
        font-size: 12px;
        padding: 15px;
      }
      .footer a {
        color: #1d2b36;
        text-decoration: underline;
      }
      .social-icons {
        margin-top: 10px;
      }
      .social-icons a {
        margin: 0 5px;
        text-decoration: none;
      }
      .social-icons img {
        width: 24px;
        height: 24px;
      }
      .disclaimer {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        font-size: 12px;
        color: #666666;
      }
      .membership-banner {
        background-color: #00796b;
        color: white;
        text-align: center;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
      }
      .membership-banner img {
        max-width: 50px;
        vertical-align: middle;
        margin-right: 10px;
      }
      .membership-banner a {
        color: #ffeb3b;
        font-weight: bold;
        text-decoration: none;
        margin-left: 10px;
        display: inline-block;
      }
      .right-block {
        width: 50%;
      }
      .left-block {
        width: 50%;
      }
      .order-success {
        color: #0066cc;
      }
      .order-cancelled {
        color: #c75453;
      }
      .right_align {
        text-align: right;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <a href="https://dentalkart.onelink.me/JiHT/Homepage">
        <img
          src="https://dentalkart-media.s3.ap-south-1.amazonaws.com/Frame+1000004383.gif"
          alt=""
          width="100%"
        />
      </a>
      <div class="content">
        <h2
          class="<%= (status === 'new_order' || status === 'payment_received') ? 'order-success' : 'order-cancelled' %>"
        >
          <% if (status === 'new_order' || status === 'payment_received') { %>
          Order Placed Successfully <% } else { %>Order Cancelled <% } %>
        </h2>
        <p>Dear Dr <%=userName%></p>
        <p>
          <% if (status === 'new_order' || status === 'payment_received') { %>
          Your order from Dentalkart.com is confirmed. Below are the details of
          your shipments: <% } else { %>We regret to inform you that your recent
          order from Dentalkart.com has been cancelled. Below are the details of
          your cancelled order: <% } %>
        </p>
        <p>
          If you have any questions about your order, you can email us at
          <a
            href="mailto:<EMAIL>"
            style="color: #0066cc; text-decoration: none"
            ><EMAIL></a
          >
          or call us at <strong>+91-728-9999-456</strong>.
        </p>

        <div class="order-info">
          <div class="left-block">
            <h3>Order ID: <strong>#<%=orderId%></strong></h3>
            <p><strong>Order Placed:</strong> <%=orderDate%></p>
          </div>
          <div class="right-block">
            <h3>
              <% if (status === 'new_order' || status === 'payment_received') {
              %> Payment Mode: <% } else { %> Cancellation Date: <% } %>
              <strong>
                <strong>
                  <strong>
                    <% if (status === 'new_order') { %> Cash on Delivery <% }
                    else if (status === 'payment_received') { %> Online Payment
                    <% } else { %> <%=cancelUpdateDate%> <% } %>
                  </strong>
                </strong>
              </strong>
            </h3>
          </div>
        </div>
        <div class="address-section">
          <div class="column">
            <h3>Shipping Address</h3>
            <p><strong><%=shippingAddress.firstname%></strong></p>
            <p>
              <%=shippingAddress.street%>, <%=shippingAddress.city%>,
              <%=shippingAddress.region%> - <%=shippingAddress.postcode%>
            </p>
            <p><strong>Phone:</strong> +91-<%=shippingAddress.telephone%></p>
          </div>
          <div class="column">
            <h3>Billing Address</h3>
            <p><strong><%=billingAddress.firstname%></strong></p>
            <p>
              <%=billingAddress.street%>, <%=billingAddress.city%>,
              <%=billingAddress.region%> - <%=billingAddress.postcode%>
            </p>
            <p><strong>Phone:</strong> +91-<%=billingAddress.telephone%></p>
          </div>
        </div>

        <h3>Order Details</h3>
        <table class="order-details">
          <thead>
            <tr>
              <th>Product</th>
              <th>Qty</th>
              <th>Unit Price</th>
              <th>Total Price</th>
            </tr>
          </thead>
          <tbody>
            <% items.forEach(function(item) { %>
            <tr>
              <td class="product"><%= item.productName %></td>
              <td><%= Math.floor(item.quantity) %></td>
              <td>
                <%= item.is_free_product ? 'Free' : Math.floor(item.price) %>
              </td>
              <td>
                <%= item.is_free_product ? 'Free' : Math.floor(item.quantity *
                item.price) %>
              </td>
            </tr>
            <% }) %>
          </tbody>
        </table>
        <div class="price-distribution">
          <div class="price-detail">
            <p class="left-block">Item Total:</p>
            <p class="right-block right_align">Rs. <%= subTotal %></p>
          </div>
          <div class="price-detail">
            <p class="left-block">Delivery Partner Fee:</p>
            <p class="right-block right_align">Rs. <%= shippingCharge %></p>
          </div>
          <% if (handlingFee > 0) { %>
          <div class="price-detail">
            <p class="left-block">Overweight Delivery Charges:</p>
            <p class="right-block right_align">Rs. <%= handlingFee %></p>
          </div>
          <% } %> <% if (couponDiscount > 0) { %>
          <div class="price-detail">
            <p class="left-block">Coupons Discount:</p>
            <p class="right-block right_align">Rs. <%= couponDiscount %></p>
          </div>
          <% } %> <% if (rewardDiscount > 0) { %>
          <div class="price-detail">
            <p class="right-block">Reward Discount:</p>
            <p class="right-block right_align">Rs. <%= rewardDiscount %></p>
          </div>
          <% } %>
          <div class="price-detail">
            <p class="left-block"><span>Grand Total:</span></p>
            <p class="right-block right_align">
              <span>Rs. <%= grandTotal %></span>
            </p>
          </div>
        </div>
        <div class="button-container">
          <% if (status === 'cancelled') { %>
          <a
            href="https://dentalkart.com/track-page/<%= orderId %>"
            class="button"
          >
            View Order Details
          </a>
          <% } else { %>
          <a
            href="https://dentalkart.com/track-page/<%= orderId %>"
            class="button"
          >
            Track Your Order
          </a>
          <% } %>
        </div>
        <% if (earnedPoints > 0) { %>
        <div class="rewards">
          <p>
            You've earned <strong><%= earnedPoints %> reward coins</strong> with
            this order!
          </p>
        </div>
        <% } %>
        <div class="disclaimer">
          <p>
            <strong>
              Note : Invoice link will be sent once the order is dispatched
            </strong>
          </p>
          <p>
            <strong>Disclaimer:</strong> This is an auto-generated email. Please
            do not reply to this email. If you have any questions or need
            further assistance, please contact our customer service at
            <a
              href="mailto:<EMAIL>"
              style="color: #0066cc; text-decoration: none"
              ><EMAIL></a
            >
            or call us at <strong>+91-728-9999-456</strong>.
          </p>
        </div>

        <div class="membership-banner">
          <p>
            Enjoy exclusive benefits with Dentalkart membership!
            <a href="https://dentalkart.com/membership"
              >Learn more and join now</a
            >
          </p>
        </div>
      </div>
      <div class="footer">
        <p>© 2024 Dentalkart. All rights reserved.</p>
        <p>
          For assistance, visit our
          <a href="https://dentalkart.com/help">Help Center</a> or contact us at
          <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>
        <div class="social-icons">
          <a href="https://www.facebook.com/dentalkart">
            <img
              src="https://cdn-icons-png.flaticon.com/512/1384/1384005.png"
              alt="Facebook"
            />
          </a>
          <a href="https://www.instagram.com/dentalkart/">
            <img
              src="https://cdn-icons-png.flaticon.com/512/1384/1384063.png"
              alt="Instagram"
            />
          </a>
          <a href="https://twitter.com/dentalkart">
            <img
              src="https://cdn-icons-png.flaticon.com/512/1384/1384017.png"
              alt="Twitter"
            />
          </a>
          <a href="https://www.linkedin.com/company/dentalkart">
            <img
              src="https://cdn-icons-png.flaticon.com/512/1384/1384088.png"
              alt="LinkedIn"
            />
          </a>
        </div>
      </div>
      <p class="disclaimer">
        This email was sent to you because you made a purchase on
        Dentalkart.com. If you wish to unsubscribe from our promotional emails,
        please
        <a
          href="http://tracking.dentalkart.com/tracking/unsubscribe?d=Dd6ncBHKqM0MQk_oXyERl9ET_994IgtC9_LLnxXuQcLrBw6LokiKLGh_wqlfo-7ehj6YIHmW3AJrAFOe8ZYbMTAXSMzYXtHpz0gf-nKOPTLO0"
          >click here</a
        >.
      </p>
    </div>
  </body>
</html>
