export interface RazorpayCreateOrderSuccessResponse {
  id: string; // uniquue razorpay order id
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  offer_id: null;
  status: string;
  attempts: number;
  notes: any[];
  created_at: number;
}

export interface RazorpayCreateOrderErrorResponse {
  statusCode: number;
  error: {
    code: string;
    description: string;
    source: string;
    step: string;
    reason: string;
    metadata: object;
    field: string;
  };
}

export interface RazorpayCreateOrderResponse
  extends RazorpayCreateOrderSuccessResponse,
    RazorpayCreateOrderErrorResponse {}

export interface RazorpayPaymentsFetchResponse {
  id: string;
  entity: string;
  amount: number;
  currency: string;
  status: string;
  order_id: string;
  invoice_id: string;
  international: boolean;
  method: string;
  amount_refunded: number;
  refund_status: string;
  captured: boolean;
  description: string;
  card_id: string;
  bank: string;
  wallet: string;
  vpa: string;
  email: string;
  contact: string;
  notes: object;
  fee: number;
  tax: number;
  error_code: string;
  error_description: string;
  error_source: string;
  error_step: string;
  error_reason: string;
  acquirer_data: { auth_code: string };
  created_at: number;
}

export interface RazorpayPaymentsCaptureResponse {
  id: string;
  entity: string;
  amount: number;
  currency: string;
  status: string;
  order_id: string;
  invoice_id: string;
  international: boolean;
  method: string;
  amount_refunded: number;
  refund_status: string;
  captured: true;
  description: string;
  card_id: string;
  bank: string;
  wallet: string;
  vpa: string;
  email: string;
  contact: string;
  notes: object;
  fee: number;
  tax: number;
  error_code: string;
  error_description: string;
  error_source: string;
  error_step: string;
  error_reason: string;
  acquirer_data: {
    rrn: string;
    upi_transaction_id: string;
  };
  created_at: number;
}
