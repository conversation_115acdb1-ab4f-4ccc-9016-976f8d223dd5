export interface ExtensionAttributeInput {
  attribute_code: string;
  value: string;
}

export interface PlaceOrderRequestInterface {
  cart_id: string;
  payment_method: string;
  extension_attributes?: [ExtensionAttributeInput];
}

export interface AdminPlaceOrderRequestInterface {
  cart_id: string;
  customer_id: number;
  payment_method: string;
  extension_attributes?: [ExtensionAttributeInput];
  payment_reference?: string;
}
