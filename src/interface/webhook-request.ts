export interface PaymentEntityInterface {
  acquirer_data: {
    bank_transaction_id: string;
  };
  amount: number;
  amount_refunded: number;
  bank: string;
  captured: boolean;
  card_id: string;
  contact: string;
  created_at: number;
  currency: string;
  description: string;
  email: string;
  entity: string;
  error_code: string;
  error_description: string;
  error_reason: string;
  error_source: string;
  error_step: string;
  fee: number;
  id: string;
  invoice_id: string;
  international: boolean;
  notes: any[];
  method: string;
  order_id: string;
  refund_status: string;
  status: string;
  tax: string;
  vpa: string;
  wallet: string;

  // only received in capture event
  amount_transferred: number;
  base_amount: number;
}

export interface PaymentWebhookRequestInterface {
  account_id: string;
  entity: string;
  event: string;
  contains: string[];
  created_at: number;
  payload: {
    payment: {
      entity: PaymentEntityInterface;
    };
  };
}
