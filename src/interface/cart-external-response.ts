export interface GuestCartResponse {
  id: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  is_active_membership: boolean;
  is_virtual: boolean;
  items_count: number;
  items_qty: number;
  customer: CustomerDetails;
  items: CartItem[];
  billing_address: {
    id: number;
    region: string;
    region_id: string;
    region_code: string;
    country_id: string;
    street: string[];
    telephone: string;
    postcode: string;
    latitude?: number;
    longitude?: number;
    tag?: string;
    map_address?: string;
    customer_street_2?: string;
    city: string;
    firstname: string;
    lastname: string;
    email: string;
    same_as_billing: number;
    save_in_address_book: number;
    gst_id: string;
  };
  orig_order_id: number;
  currency: {
    global_currency_code: string;
    base_currency_code: string;
    store_currency_code: string;
    quote_currency_code: string;
    store_to_base_rate: number;
    store_to_quote_rate: number;
    base_to_global_rate: number;
    base_to_quote_rate: number;
  };
  customer_is_guest: boolean;
  customer_note_notify: boolean;
  customer_tax_class_id: number;
  store_id: number;
  extension_attributes: { shipping_assignments: ShippingAssignment[] };
  applied_rule_ids: string;
  quote_amount_promotions?: CartItemPromotion[];
}

export interface CartItem {
  item_id: number;
  sku: string;
  qty: number;
  name: string;
  price: number;
  product_type: string;
  quote_id: string;
  referral_code: string;
  is_free_product: boolean;
  parent_id: number;
  quote_item_promotions?: CartItemPromotion[];
  buying_guide_qty: number;
}

export interface CartItemPromotion {
  promotion_id: number;
  sku: string;
  qty: number;
  meta_info: Record<string, any>;
}

export interface ShippingAssignment {
  shipping: {
    address: {
      id: number;
      region: string;
      region_id: string;
      region_code: string;
      country_id: string;
      street: string[];
      telephone: string;
      postcode: string;
      city: string;
      latitude?: number;
      longitude?: number;
      tag?: string;
      map_address?: string;
      customer_street_2?: string;
      firstname: string;
      lastname: string;
      email: string;
      same_as_billing: number;
      save_in_address_book: number;
      gst_id: string;
    };
    method: string;
  };
  items: [
    {
      item_id: number;
      sku: string;
      qty: number;
      name: string;
      price: number;
      product_type: string;
      quote_id: string;
    },
  ];
}

export interface AvailablePaymentMethods {
  code: string;
  title: string;
}

export interface CartPaymentInfoResponse {
  payment_methods: AvailablePaymentMethods[];
  totals: {
    coupon_code: string;
    grand_total: number;
    base_grand_total: number;
    subtotal: number;
    base_subtotal: number;
    discount_amount: number;
    base_discount_amount: number;
    subtotal_with_discount: number;
    base_subtotal_with_discount: number;
    shipping_amount: number;
    base_shipping_amount: number;
    shipping_discount_amount: number;
    base_shipping_discount_amount: number;
    tax_amount: number;
    base_tax_amount: number;
    weee_tax_applied_amount: number;
    shipping_tax_amount: number;
    base_shipping_tax_amount: number;
    subtotal_incl_tax: number;
    shipping_incl_tax: number;
    base_shipping_incl_tax: number;
    base_currency_code: string;
    quote_currency_code: string;
    items_qty: number;
    items: PyamentInfoItem[];
    total_segments: PaymentInfoTotalSegments[];
    extension_attributes: {
      mp_membership: string;
    };
  };
}

export interface PyamentInfoItem {
  item_id: number;
  price: number;
  base_price: number;
  qty: number;
  row_total: number;
  base_row_total: number;
  row_total_with_discount: number;
  tax_amount: number;
  base_tax_amount: number;
  tax_percent: number;
  discount_amount: number;
  base_discount_amount: number;
  discount_percent: number;
  price_incl_tax: number;
  base_price_incl_tax: number;
  row_total_incl_tax: number;
  base_row_total_incl_tax: number;
  options: string;
  weee_tax_applied_amount: number;
  weee_tax_applied: number;
  name: string;
}

export interface PaymentInfoTotalSegments {
  code: string;
  title: string;
  value: number;
  area?: string;
}

export interface productsForService {
  id: number;
  thumbnail_url: string;
  type_id: string;
  sku: string;
  url_key: string;
  name: string;
  weight: number;
  reward_point_product: number;
  dentalkart_custom_fee: number;
  pd_expiry_date: string;
  is_saleable: boolean;
  error_msg: string[];
  is_cod: string;
  price: {
    minimalPrice: {
      amount: {
        value: number;
      };
    };
    regularPrice: {
      amount: {
        value: number;
      };
    };
  };
}

export interface productsGQLResponse {
  productForService: productsForService[];
}

export interface CustomerDetails {
  id: number;
  group_id: number;
  default_billing: string;
  default_shipping: string;
  created_at: string;
  updated_at: string;
  created_in: string;
  email: string;
  firstname: string;
  lastname: string;
  gender: number;
  store_id: number;
  taxvat: string;
  website_id: 1;
  addresses: CustomerAddress[];
  disable_auto_group_change: number;
  extension_attributes: {
    is_subscribed: boolean;
  };
  custom_attributes: CustomerCustomAttribute[];
}

export interface CustomerCustomAttribute {
  attribute_code: 'register_platform' | 'register_type';
  value: 'email' | 'web';
}

export interface CustomerAddress {
  id: number;
  customer_id: number;
  region: {
    region_code: string;
    region: string;
    region_id: number;
  };
  region_id: number;
  country_id: string;
  street: string;
  telephone: string;
  postcode: string;
  city: string;
  firstname: string;
  lastname: string;
  default_shipping?: boolean;
  default_billing?: boolean;
}

export interface BuyNowCartResponse {
  cart_info: GuestCartResponse;
  payment_info: CartPaymentInfoResponse;
}
