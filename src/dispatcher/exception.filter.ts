import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { logger } from 'src/utils/service-logger';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    logger.error('exception filter', exception);
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    if (exception instanceof HttpException) {
      status = exception.getStatus();
    }

    if (exception.response.message) {
      const respMsg = Array.isArray(exception.response.message)
        ? exception.response.message[0]
        : exception.response.message;
      return response.status(status).json({
        isError: true,
        message: respMsg || 'Internal server error',
        data: exception.response.data || {},
      });
    }

    return response.status(status).json({
      isError: true,
      message: exception.response,
      data: {},
    });
  }
}
