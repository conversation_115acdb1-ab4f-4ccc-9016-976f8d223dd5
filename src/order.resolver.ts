import {
  BadRequestException,
  Inject,
  UnauthorizedException,
} from '@nestjs/common';
import { Resolver, Mutation, Args, Context, Query } from '@nestjs/graphql';
import { SalesOrder } from './database/entities/sales-order';
import { SalesOrderAddress } from './database/entities/sales-order-address';
import { SalesOrderItem } from './database/entities/sales-order-item';
import { SalesOrderPayment } from './database/entities/sales-order-payment';
import { SalesOrderTax } from './database/entities/sales-order-tax';
import { PlaceOrderRequestInterface } from './interface/place-order-request';
import { OrderMapper } from './mapper/order';
import { OrderHelper } from './utils/order.helper';
import { logger } from './utils/service-logger';
import { Sequelize } from 'sequelize-typescript';
import { SalesOrderTaxItem } from './database/entities/sales-order-tax-item';
import { PaymentMethods, UserTypes } from './config/constants';
import { MyOrderRequest } from './interface/my-order-request';
import { Op } from 'sequelize';
import { FetchOrderInputRequest } from './interface/fetch-order-request';
import { UpdateOrderStatusInputRequest } from './interface/update-order-status';
import { find } from 'lodash';
import { NotifyERP } from './utils/notify-erp';
import { RazorpayService } from './razorpay/razorpay.service';
import { OrderStatuses } from './config/constants';
import { SalesOrderExtraInfo } from './database/entities/sales-order-extra-info';
import { OrderItemExtraInfo } from './database/entities/order-item-extra-info';
import { SalesOrderItemPromotion } from './database/entities/sales-order-item-promotion';
import { SalesOrderAmountPromotion } from './database/entities/sales-order-amount-promotion';

@Resolver('Order')
export class OrderResolver {
  constructor(
    private readonly orderHelper: OrderHelper,
    private readonly orderMapper: OrderMapper,
    private readonly notifyERP: NotifyERP,
    private readonly razorpayService: RazorpayService,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}

  @Mutation()
  async dkplaceOrderV2(
    @Args() request: { input: PlaceOrderRequestInterface },
    @Context() context: any,
  ) {
    const authToken = this.orderHelper.getUserToken(
      context.req.headers['authorization'],
    );

    const platform = context.req.headers?.['platform'] ?? '';
    const version = context.req.headers?.['version'] ?? null;

    let userType = UserTypes.GUEST;
    if (authToken) userType = UserTypes.REGISTERED;
    const cartExists = await this.orderHelper.checkCartExistence(
      userType,
      request.input.cart_id,
      authToken,
      platform,
      version,
    );

    if (!cartExists || !cartExists.items) {
      return new BadRequestException('Invalid cartId');
    }
    if (cartExists && cartExists.customer.id && !authToken) {
      throw new UnauthorizedException('Unauthorized user');
    }
    const cartPayment = await this.orderHelper.getPaymentAndShippingDetails(
      userType,
      request.input.cart_id,
      authToken,
      platform,
      version,
    );

    // validate payment_method received in request
    if (
      cartPayment.payment_methods
        .map((o) => o.code)
        .indexOf(request.input.payment_method) === -1
    ) {
      return new BadRequestException('Invalid payment method');
    }

    const skus = this.orderHelper.extractAllSkus(cartExists);
    const products = await this.orderHelper.getProductDataFromSku(
      skus,
      cartExists?.extension_attributes?.shipping_assignments?.[0]?.shipping
        ?.address?.country_id || cartExists?.billing_address?.country_id,
    );

    const responseData = {
      amount: this.orderHelper.roundAndToBaseCurrencyAmount(
        Number(
          find(cartPayment.totals.total_segments, { code: 'grand_total' })
            ?.value,
        ) || 0,
        2,
        100,
      ),
      currency: cartExists.currency.quote_currency_code,
      merchant_id: '',
      order_number: '',
      payment_method: request.input.payment_method,
      reference_number: '',
    };

    const orderObj = await this.orderMapper.buildSalesOrderObj(
      cartExists,
      cartPayment,
      context?.req?.ip || null,
      request.input.payment_method,
      platform,
      version,
    );
    const t1 = await this.sequelize.transaction();
    try {
      const salesOrderObj = await SalesOrder.create(orderObj, {
        transaction: t1,
      });

      const { billing_address, shipping_address } =
        this.orderMapper.buildSalesOrderAddressObj(
          cartExists,
          salesOrderObj.order_id,
        );

      //validate billing shipping address
      this.orderHelper.validateOrderAdddress(
        billing_address,
        request.input.cart_id,
      );
      if (shipping_address) {
        this.orderHelper.validateOrderAdddress(
          shipping_address,
          request.input.cart_id,
        );
      }

      const salesOrderAddresses = await Promise.all([
        await SalesOrderAddress.create(billing_address, {
          transaction: t1,
        }),
        shipping_address &&
          (await SalesOrderAddress.create(shipping_address, {
            transaction: t1,
          })),
      ]);

      // Get EDD info first using product IDs from products.productForService
      const eddInfo = await this.orderHelper.getEDDInfo(
        salesOrderObj,
        products?.productForService?.map((p) => ({ product_id: p.id })) || [], // Create mock items with product_id
        salesOrderAddresses,
        products?.productForService,
      );

      const orderTaxObj = this.orderMapper.buildSalesOrderTaxObj(
        cartPayment,
        salesOrderObj.order_id,
      );
      const salesTaxObj = await SalesOrderTax.create(orderTaxObj, {
        transaction: t1,
      });
      const orderItemObj = this.orderMapper.buildSalesOrderItemObj(
        cartExists,
        cartPayment,
        salesOrderObj.order_id,
        products,
        salesTaxObj.tax_id,
        eddInfo?.delivery_days, // Pass delivery days to include in order items
      );
      const salesOrderItems = await SalesOrderItem.bulkCreate(orderItemObj, {
        include: [
          { model: SalesOrderTaxItem, as: 'tax' },
          { model: OrderItemExtraInfo, as: 'itemExtraInfo' },
          { model: SalesOrderItemPromotion, as: 'salesOrderItemPromotions' },
        ],
        transaction: t1,
      });

      // Map and create cart amount promotions if they exist
      const amountPromotions = this.orderMapper.mapAmountPromotions(
        cartExists,
        salesOrderObj.order_id,
        products,
      );

      if (amountPromotions.length > 0) {
        salesOrderObj.salesOrderAmountPromotions =
          await SalesOrderAmountPromotion.bulkCreate(amountPromotions, {
            transaction: t1,
          });
      }

      let razorpayOrder;
      if (request.input.payment_method === PaymentMethods.RAZOR_PAY) {
        razorpayOrder = await this.razorpayService.createOrder({
          amount: this.orderHelper.roundAndToBaseCurrencyAmount(
            Number(salesOrderObj.grand_total) || 0,
            2,
            100,
          ),
          currency: salesOrderObj.order_currency_code,
          paymentId: salesOrderObj.order_id,
          orderId: salesOrderObj.increment_id,
        });
        responseData.merchant_id = razorpayOrder.merchantId;
        responseData.reference_number = razorpayOrder.referenceNumber;
      }
      const orderPaymentObj = this.orderMapper.buildSalesOrderPaymentObj(
        cartPayment,
        salesOrderObj.order_id,
        request.input.payment_method,
        razorpayOrder,
      );
      const salesOrderPayment = await SalesOrderPayment.create(
        orderPaymentObj,
        { transaction: t1 },
      );

      // build and save sales_order_extra_info values
      let sales_order_extra_info = null;
      if (eddInfo || request?.input?.extension_attributes?.length) {
        const sales_order_extra_info_obj =
          this.orderMapper.buildSalesExtraInfoObject(
            salesOrderObj.order_id,
            request.input.extension_attributes,
            eddInfo,
          );
        sales_order_extra_info = await SalesOrderExtraInfo.create(
          sales_order_extra_info_obj,
          { transaction: t1 },
        );
      }

      await this.orderHelper.inactiveCart(
        request.input.cart_id,
        platform,
        version,
      );
      await t1.commit();
      responseData.order_number = salesOrderObj.increment_id;

      const isFirstOrder = await this.orderHelper.isFirstOrder(
        salesOrderObj.customer_id,
      );

      // Notify ERP about order creation
      this.notifyERP.notifyStatusUpdate(
        salesOrderObj,
        salesOrderItems,
        salesOrderAddresses,
        salesOrderPayment,
        null,
        sales_order_extra_info,
        true,
        isFirstOrder,
      );

      //Update coupon usgae api call
      this.orderHelper.updateRuleUsage(salesOrderObj);
    } catch (error) {
      await t1.rollback();
      // logger.error('Place order transaction error', error);
      console.log(
        `PlaceOrderError-${request?.input?.cart_id}`,
        error?.message || error,
      );
      this.orderHelper.handleServiceError(
        error,
        `Error in Order resolver order for cart ID ${request?.input?.cart_id}`,
      );
    }

    return responseData;
  }

  @Query()
  async dkcustomerOrdersV2(
    @Args() request: MyOrderRequest,
    @Context() context: any,
  ) {
    const authToken = this.orderHelper.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new BadRequestException('Unauthorized user');
    }
    try {
      const customer = await this.orderHelper.getCustomerDetails(authToken);
      const where = { customer_id: +customer.customer_id };
      if (request?.timespan) {
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - request?.timespan);
        startDate.setHours(0, 0, 0, 0);
        where['createdAt'] = {
          [Op.lte]: Date.now(),
          [Op.gte]: startDate.valueOf(),
        };
      }
      const userOrders = await SalesOrder.findAll({
        where,
        include: [SalesOrderItem],
      });
      const orders = userOrders.map((o) => this.orderMapper.mapUserOrder(o));
      return { orders };
    } catch (error) {
      logger.error('Error in myOrders query', error);
      throw new Error('Internal server error');
    }
  }

  @Query()
  async fetchOrderV2(
    @Args() request: { input: FetchOrderInputRequest },
    @Context() context: any,
  ) {
    const authToken = this.orderHelper.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new UnauthorizedException('Unauthorized user');
    }
    const customer = await this.orderHelper.getCustomerDetails(authToken);
    if (!customer) throw new UnauthorizedException('Unauthorized user');
    const order = await SalesOrder.findOne({
      where: {
        increment_id: request?.input?.order_id,
        customer_id: +customer.customer_id,
      },
      include: [SalesOrderPayment],
    });
    if (
      !order ||
      (order?.payment?.method === PaymentMethods.RAZOR_PAY &&
        ((request?.input?.rzp_order_id &&
          order?.payment.razorpay_order_id !== request?.input?.rzp_order_id) ||
          (request?.input?.rzp_payment_id &&
            order?.payment.razorpay_payment_id !==
              request?.input?.rzp_payment_id)))
    ) {
      const platform = context?.req?.headers?.['platform'] ?? '';
      const version = context?.req?.headers?.['version'] ?? null;

      throw new BadRequestException('Order not found');
    }

    return this.orderMapper.buildFetchOrderResponse(order);
  }

  @Query()
  async fetchOrderGuestV2(
    @Args() request: { input: FetchOrderInputRequest; email: string },
  ) {
    const order = await SalesOrder.findOne({
      where: {
        increment_id: request?.input?.order_id,
        customer_email: request?.email,
        customer_is_guest: true,
      },
      include: [SalesOrderPayment],
    });
    if (
      !order ||
      (order?.payment?.method === PaymentMethods.RAZOR_PAY &&
        ((request?.input?.rzp_order_id &&
          order?.payment.razorpay_order_id !== request?.input?.rzp_order_id) ||
          (request?.input?.rzp_payment_id &&
            order?.payment.razorpay_payment_id !==
              request?.input?.rzp_payment_id)))
    ) {
      throw new BadRequestException('Order not found');
    }
    return this.orderMapper.buildFetchOrderResponse(order);
  }

  @Mutation()
  async updateOrderStatus(
    @Args() request: { input: UpdateOrderStatusInputRequest },
    @Context() context: any,
  ) {
    const apiKey = context.req.headers['x-api-key'];

    if (!apiKey || apiKey != 'da2-b7vqajjrfbgbvjf4fbeupdatestatus') {
      throw new UnauthorizedException('Unauthorized user');
    }

    try {
      const order = await SalesOrder.findOne({
        where: {
          increment_id: request?.input?.order_id,
        },
      });
      if (order) {
        const isStatusValid = Object.values(OrderStatuses).includes(
          request.input.status,
        );

        const canUpdateStatus = await this.orderHelper.canUpdateStatus(order);

        if (
          isStatusValid &&
          canUpdateStatus &&
          request.input.status === OrderStatuses.CANCELLED
        ) {
          const updatedData = await SalesOrder.update(
            { status: request.input.status },
            { where: { increment_id: request.input.order_id } },
          );

          if (updatedData) {
            const orderData = await SalesOrder.findByPk(order.order_id, {
              include: [
                SalesOrderAddress,
                SalesOrderItem,
                SalesOrderPayment,
                SalesOrderAddress,
              ],
            });

            this.notifyERP.notifyStatusUpdate(
              orderData,
              orderData.items,
              orderData.address,
              orderData.payment,
            );

            return { message: 'Status updated.' };
          }

          return new BadRequestException('Something went wrong.');
        }
        return new BadRequestException('Status is not correct.');
      }
      return new BadRequestException('Order not found.');
    } catch (error) {
      throw new Error(
        error.errors?.[0]?.message ||
          error?.original?.sqlMessage ||
          'Internal server erro',
      );
    }
  }
}
