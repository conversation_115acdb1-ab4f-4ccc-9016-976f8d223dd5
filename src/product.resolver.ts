import { UnauthorizedException } from '@nestjs/common';
import { Resolver, Args, Context, Query } from '@nestjs/graphql';
import { SalesOrder } from './database/entities/sales-order';
import { SalesOrderItem } from './database/entities/sales-order-item';
import { OrderHelper } from './utils/order.helper';
import { logger } from './utils/service-logger';
import { NO_OF_MONTHS_BACK } from './config/constants';
import { Op } from 'sequelize';
import * as _ from 'lodash';

@Resolver('product')
export class ProductResolver {
  constructor(private readonly orderHelper: OrderHelper) {}

  @Query()
  async checkCustomerBoughtProduct(
    @Args() { product_id, type }: { product_id: number; type?: string },
    @Context() context: any,
  ) {
    const authToken = this.orderHelper.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new UnauthorizedException(
        'Only logged in user can access the resource.',
      );
    }
    try {
      const customer = await this.orderHelper.getCustomerDetails(authToken);
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - NO_OF_MONTHS_BACK);
      startDate.setHours(0, 0, 0, 0);

      const userOrders = await SalesOrder.findAll({
        where: {
          customer_id: +customer.customer_id,
          createdAt: {
            [Op.lte]: Date.now(),
            [Op.gte]: startDate.valueOf(),
          },
        },
        include: [SalesOrderItem],
      });

      for (const order of userOrders) {
        const productItem = _.find(order.items, { product_id: product_id });
        if (productItem?.product_id === product_id) return true;
      }
      return false;
    } catch (error) {
      logger.error('Error in checkCustomerBoughtProduct', error);
      throw new Error('Internal server error');
    }
  }
}
