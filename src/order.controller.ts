import {
  Controller,
  Get,
  Post,
  Body,
  ValidationPipe,
  Inject,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  AdminPlaceOrderRequestInterface,
  PlaceOrderRequestInterface,
} from './interface/place-order-request';
import { OrderHelper } from './utils/order.helper';
import { OrderMapper } from './mapper/order';
import { NotifyERP } from './utils/notify-erp';
import { RazorpayService } from './razorpay/razorpay.service';
import { Sequelize } from 'sequelize-typescript';
import { PaymentMethods, UserTypes } from './config/constants';
import { OrderAdminHelper } from './utils/order-admin.helper';
import { find } from 'lodash';
import { SalesOrderExtraInfo } from './database/entities/sales-order-extra-info';
import { OrderItemExtraInfo } from './database/entities/order-item-extra-info';
import { logger } from './utils/service-logger';
import { SalesOrderPayment } from './database/entities/sales-order-payment';
import { SalesOrderTaxItem } from './database/entities/sales-order-tax-item';
import { SalesOrderItem } from './database/entities/sales-order-item';
import { SalesOrderTax } from './database/entities/sales-order-tax';
import { SalesOrderAddress } from './database/entities/sales-order-address';
import { SalesOrder } from './database/entities/sales-order';

@Controller('/admin')
export class OrderAdminController {
  constructor(
    private readonly orderHelper: OrderHelper,
    private readonly orderAdminHelper: OrderAdminHelper,
    private readonly orderMapper: OrderMapper,
    private readonly notifyERP: NotifyERP,
    private readonly razorpayService: RazorpayService,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}
  @Post('/place-order')
  async dkplaceOrder(
    @Body(new ValidationPipe({ transform: true }))
    input: AdminPlaceOrderRequestInterface,
  ) {
    const cartExists = await this.orderAdminHelper.getAdminCart(
      input.customer_id,
      input.cart_id,
    );

    if (!cartExists || !cartExists.items) {
      return new BadRequestException('Invalid cartId');
    }

    const cartPayment =
      await this.orderAdminHelper.getPaymentAndShippingDetails(
        input.cart_id,
        input.customer_id,
      );
    // validate payment_method received in request
    if (
      cartPayment.payment_methods
        .map((o) => o.code)
        .indexOf(input.payment_method) === -1
    ) {
      return new BadRequestException('Invalid payment method');
    }

    const products = await this.orderHelper.getProductDataFromSku(
      cartExists.items.map((o) => o.sku),
      cartExists?.extension_attributes?.shipping_assignments?.[0]?.shipping
        ?.address?.country_id || cartExists?.billing_address?.country_id,
    );

    const responseData = {
      amount: this.orderHelper.roundAndToBaseCurrencyAmount(
        Number(
          find(cartPayment.totals.total_segments, { code: 'grand_total' })
            ?.value,
        ) || 0,
        2,
        100,
      ),
      currency: cartExists.currency.quote_currency_code,
      merchant_id: '',
      order_number: '',
      payment_method: input.payment_method,
      reference_number: input.payment_reference,
    };

    const orderObj = await this.orderMapper.buildSalesOrderObj(
      cartExists,
      cartPayment,
      null,
      input.payment_method,
    );

    const t1 = await this.sequelize.transaction();
    try {
      const salesOrderObj = await SalesOrder.create(orderObj, {
        transaction: t1,
      });

      const { billing_address, shipping_address } =
        this.orderMapper.buildSalesOrderAddressObj(
          cartExists,
          salesOrderObj.order_id,
        );

      //validate billing shipping address
      this.orderHelper.validateOrderAdddress(billing_address, input.cart_id);
      if (shipping_address) {
        this.orderHelper.validateOrderAdddress(shipping_address, input.cart_id);
      }

      const salesOrderAddresses = await Promise.all([
        await SalesOrderAddress.create(billing_address, {
          transaction: t1,
        }),
        shipping_address &&
          (await SalesOrderAddress.create(shipping_address, {
            transaction: t1,
          })),
      ]);

      // Get EDD info first using product IDs from products.productForService
      const eddInfo = await this.orderHelper.getEDDInfo(
        salesOrderObj,
        products?.productForService?.map((p) => ({ product_id: p.id })) || [], // Create mock items with product_id
        salesOrderAddresses,
        products?.productForService,
      );

      const orderTaxObj = this.orderMapper.buildSalesOrderTaxObj(
        cartPayment,
        salesOrderObj.order_id,
      );
      const salesTaxObj = await SalesOrderTax.create(orderTaxObj, {
        transaction: t1,
      });
      const orderItemObj = this.orderMapper.buildSalesOrderItemObj(
        cartExists,
        cartPayment,
        salesOrderObj.order_id,
        products,
        salesTaxObj.tax_id,
        eddInfo?.delivery_days, // Pass delivery days to include in order items
      );

      const salesOrderItems = await SalesOrderItem.bulkCreate(orderItemObj, {
        include: [
          { model: SalesOrderTaxItem, as: 'tax' },
          { model: OrderItemExtraInfo, as: 'itemExtraInfo' },
        ],
        transaction: t1,
      });
      let razorpayOrder;
      // if (input.payment_method === PaymentMethods.RAZOR_PAY) {
      //   razorpayOrder = await this.razorpayService.createOrder({
      //     amount: this.orderHelper.roundAndToBaseCurrencyAmount(
      //       Number(salesOrderObj.grand_total) || 0,
      //       2,
      //       100,
      //     ),
      //     currency: salesOrderObj.order_currency_code,
      //     paymentId: salesOrderObj.order_id,
      //     orderId: salesOrderObj.increment_id,
      //   });
      //   responseData.merchant_id = razorpayOrder.merchantId;
      //   responseData.reference_number = razorpayOrder.referenceNumber;
      // }
      const orderPaymentObj = this.orderMapper.buildSalesOrderPaymentObj(
        cartPayment,
        salesOrderObj.order_id,
        input.payment_method,
        razorpayOrder,
      );
      const salesOrderPayment = await SalesOrderPayment.create(
        orderPaymentObj,
        { transaction: t1 },
      );

      // build and save sales_order_extra_info values
      let sales_order_extra_info = null;
      if (eddInfo || input?.extension_attributes?.length) {
        const sales_order_extra_info_obj =
          this.orderMapper.buildSalesExtraInfoObject(
            salesOrderObj.order_id,
            input?.extension_attributes,
            eddInfo,
          );
        sales_order_extra_info = await SalesOrderExtraInfo.create(
          sales_order_extra_info_obj,
          { transaction: t1 },
        );
      }

      await this.orderHelper.inactiveCart(input.cart_id, 'web');
      await t1.commit();
      responseData.order_number = salesOrderObj.increment_id;

      const isFirstOrder = await this.orderHelper.isFirstOrder(
        salesOrderObj.customer_id,
      );

      // Notify ERP about order creation
      this.notifyERP.notifyStatusUpdate(
        salesOrderObj,
        salesOrderItems,
        salesOrderAddresses,
        salesOrderPayment,
        null,
        sales_order_extra_info,
        true,
        isFirstOrder,
      );

      //Update coupon usgae api call
      this.orderHelper.updateRuleUsage(salesOrderObj);
    } catch (error) {
      await t1.rollback();
      logger.error('Place order transaction error', error);
      this.orderHelper.handleServiceError(
        error,
        `Error in admin comtroller order for cart ID ${input.cart_id}`,
      );
    }

    return responseData;
  }
}
