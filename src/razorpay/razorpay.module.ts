import { Module } from '@nestjs/common';
import { NotifyERP } from 'src/utils/notify-erp';
import { UtilsModule } from 'src/utils/utils.module';
import { RazorpayController } from './razorpay.controller';
import { RazorpayService } from './razorpay.service';
import { ExternalServiceCaller } from '../utils/external-service-caller';
import { NotificationHelper } from '../utils/notification.helper';

@Module({
  imports: [UtilsModule],
  controllers: [RazorpayController],
  providers: [
    RazorpayService,
    NotifyERP,
    ExternalServiceCaller,
    NotificationHelper,
  ],
  exports: [RazorpayService],
})
export class RazorpayModule {}
