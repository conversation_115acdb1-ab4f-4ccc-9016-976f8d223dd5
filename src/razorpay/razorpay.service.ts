import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import Razorpay = require('razorpay');
import config from 'src/config/env';
import { logger } from 'src/utils/service-logger';
import {
  PaymentCreateOrderRequestBody,
  PaymentCreateOrderResponse,
} from 'src/interface/payment-service';
import { SalesOrderPayment } from 'src/database/entities/sales-order-payment';
import { SalesOrder } from 'src/database/entities/sales-order';
import { Op } from 'sequelize';
import { PaymentWebhookRequestInterface } from 'src/interface/webhook-request';
import { SalesOrderAddress } from 'src/database/entities/sales-order-address';
import { SalesOrderItem } from 'src/database/entities/sales-order-item';
import { NotifyERP } from 'src/utils/notify-erp';
import { OrderStatuses, RazorpayPaymentStatuses } from 'src/config/constants';
import {
  RazorpayCreateOrderResponse,
  RazorpayPaymentsCaptureResponse,
  RazorpayPaymentsFetchResponse,
} from 'src/interface/razorpay';
import { ProcessOrderPayload } from 'src/dto/process-order-request.dto';
import { FetchOrderStatusPayload } from 'src/dto/fetch-order-request.dto';
import { SalesOrderExtraInfo } from 'src/database/entities/sales-order-extra-info';
import { PublishStatusPayload } from '../dto/publish-order-request-dto';
import { OrderItemExtraInfo } from '../database/entities/order-item-extra-info';
import { SalesOrderItemPromotion } from 'src/database/entities/sales-order-item-promotion';
import { SalesOrderAmountPromotion } from 'src/database/entities/sales-order-amount-promotion';

@Injectable()
export class RazorpayService {
  razorPay;
  constructor(private readonly notifyERP: NotifyERP) {
    this.razorPay = new Razorpay({
      key_id: config.razorpay.api_key,
      key_secret: config.razorpay.api_secret,
    });
  }

  /**
   * It generates an order in razorpay
   * @param request CreateOrderRequestDto
   * @returns
   */
  async createOrder(
    request: PaymentCreateOrderRequestBody,
  ): Promise<PaymentCreateOrderResponse> {
    try {
      if (
        request.amount &&
        request.currency &&
        request.orderId &&
        request.paymentId
      ) {
        if (request.amount < 1)
          throw new BadRequestException('amount must be non-zero');
        const options = {
          amount: request.amount,
          currency: request.currency,
          receipt: 'receipt_' + request.paymentId,
          notes: { orderId: request.orderId },
        };
        const orderDetails: RazorpayCreateOrderResponse =
          await this.razorPay.orders.create(options);

        return {
          referenceNumber: orderDetails.id,
          merchantId: config.razorpay.api_key,
          receiptId: orderDetails.receipt,
          notes: orderDetails.notes,
        };
      } else {
        logger.error(
          `create order request paylaod is not of valid type`,
          request,
        );
        throw new BadRequestException('Please provide valid reqest payload ');
      }
    } catch (error) {
      logger.error('Razorpay create order error', error);
      if (error?.statusCode === 400)
        throw new BadRequestException(error?.error?.description);
      else throw new InternalServerErrorException(error?.error?.description);
    }
  }

  /**
   * It will handle webhook response for different
   * payment events such as capture, authorize & failed
   * @param request PaymentWebhookRequestInterface
   * @param eventId string
   * @returns
   */
  async paymentWebhook(
    request: PaymentWebhookRequestInterface,
    eventId: string,
    webhookSecret: string,
  ) {
    const razorPayOrder = await SalesOrderPayment.findOne({
      where: {
        razorpay_order_id: request?.payload?.payment?.entity?.order_id,
      },
    });
    if (!razorPayOrder) return;
    switch (request?.event?.split('.')?.[1]) {
      case RazorpayPaymentStatuses.AUTHORIZED:
        await this.authorizePayment(
          request,
          eventId,
          razorPayOrder,
          webhookSecret,
        );
        break;
      case RazorpayPaymentStatuses.CAPTURED:
        await this.capturePayment(
          request,
          eventId,
          razorPayOrder,
          webhookSecret,
        );
        break;
      case RazorpayPaymentStatuses.FAILED:
        await this.failedPayment(
          request,
          eventId,
          razorPayOrder,
          webhookSecret,
        );
        break;
      default:
        break;
    }
    return;
  }

  /**
   * It updates order & payment details for authorized payment
   * @param request PaymentWebhookRequestInterface
   * @param eventId unique razorpay header event id
   * @returns
   */
  async authorizePayment(
    request: PaymentWebhookRequestInterface,
    eventId: string,
    razorPayOrder: SalesOrderPayment,
    webhookSecret: string,
  ) {
    try {
      const salesOrder = await SalesOrder.findByPk(razorPayOrder.order_id);
      if (!salesOrder || salesOrder.status === OrderStatuses.PAYMENT_AUTHORIZE)
        return;
      const paymentDetails: RazorpayPaymentsFetchResponse =
        await this.razorPay.payments.fetch(
          request?.payload?.payment?.entity?.id,
        );
      let orderPayload = { status: OrderStatuses.PAYMENT_AUTHORIZE };
      let orderPaymentPayload: any = {
        razorpay_event_id_authorize: eventId,
        acquirer_data: request.payload.payment.entity.acquirer_data,
        notes: request.payload.payment.entity.notes,
        amount_authorized: request.payload.payment.entity.amount,
        base_amount_authorized: request.payload.payment.entity.amount,
        webhook_secret: webhookSecret,
        razorpay_payment_id: request.payload.payment.entity.id,
      };
      // manual capture payment if not already captured
      if (paymentDetails.status !== RazorpayPaymentStatuses.CAPTURED) {
        const updatedOrder = await this.manualCapturePayment(
          paymentDetails,
          orderPayload,
          orderPaymentPayload,
        );
        orderPayload = updatedOrder.orderPayload;
        orderPaymentPayload = updatedOrder.orderPaymentPayload;
      }
      await Promise.all([
        SalesOrder.update(orderPayload, {
          where: {
            order_id: razorPayOrder.order_id,
            status: OrderStatuses.PAYMENT_PENDING,
          },
        }),
        SalesOrderPayment.update(orderPaymentPayload, {
          where: { payment_id: razorPayOrder.payment_id },
        }),
      ]);
      if (orderPayload.status === OrderStatuses.PAYMENT_RECEIVED) {
        // to notify ERP about order status change
        const order = await SalesOrder.findByPk(razorPayOrder.order_id, {
          include: [
            SalesOrderAddress,
            SalesOrderPayment,
            SalesOrderAmountPromotion,
            {
              model: SalesOrderExtraInfo,
              attributes: [
                'registration_no',
                'exp_delivery_days',
                'exp_dispatch_days',
                'extra_info_entity_id',
                'processed_at',
                'max_delivery_warehouse_code',
                'extra_info_entity_id',
              ],
            },
            {
              model: SalesOrderItem,
              include: [
                {
                  model: OrderItemExtraInfo,
                  attributes: ['referral_code', 'is_free_product', 'id'],
                },
                SalesOrderItemPromotion,
              ],
            },
          ],
        });

        this.notifyERP.notifyStatusUpdate(
          order,
          order.items,
          order.address,
          order.payment,
          request.payload.payment.entity.status,
          order.sales_order_extra_info,
        );
      }
      return;
    } catch (error) {
      logger.error('authorize payment webhook', error);
      return;
    }
  }

  /**
   * Make a request to razorpay to manually capture payment
   * @param paymentDetails RazorpayPaymentsFetchResponse
   * @param orderPayload payload to update in sales_order table
   * @param orderPaymentPayload payload to update in sales_order_payment table
   * @returns
   */
  async manualCapturePayment(
    paymentDetails: RazorpayPaymentsFetchResponse,
    orderPayload: { status: string },
    orderPaymentPayload: any,
  ) {
    try {
      const captureResponse: RazorpayPaymentsCaptureResponse =
        await this.razorPay.payments.capture(
          paymentDetails.id,
          paymentDetails.amount,
          paymentDetails.currency,
        );
      orderPayload.status = OrderStatuses.PAYMENT_RECEIVED;
      orderPaymentPayload = {
        ...orderPaymentPayload,
        amount_paid: captureResponse.amount,
        base_amount_paid: captureResponse.amount,
        base_amount_paid_online: captureResponse.amount,
        acquirer_data: captureResponse.acquirer_data,
        notes: captureResponse.notes,
      };
    } catch (error) {
      const isUpdateStatus = await this.handleMannualCaptureError(
        orderPaymentPayload.razorpay_payment_id,
        error,
      );
      if (isUpdateStatus) {
        orderPayload.status = OrderStatuses.PAYMENT_RECEIVED;
      }
      logger.error('Error in manual capture payment', error);
    } finally {
      return { orderPayload, orderPaymentPayload };
    }
  }

  /**
   * It updates order & payment details for failed payment
   * @param request PaymentWebhookRequestInterface
   * @param eventId unique razorpay header event id
   * @returns
   */
  async failedPayment(
    request: PaymentWebhookRequestInterface,
    eventId: string,
    razorPayOrder: SalesOrderPayment,
    webhookSecret: string,
  ) {
    try {
      const salesOrder = await SalesOrder.findByPk(razorPayOrder.order_id);
      if (!salesOrder) return;
      await Promise.all([
        SalesOrder.update(
          {
            status: OrderStatuses.PAYMENT_PENDING,
          },
          {
            where: {
              order_id: razorPayOrder.order_id,
              status: { [Op.ne]: OrderStatuses.PAYMENT_RECEIVED },
            },
          },
        ),
        SalesOrderPayment.update(
          {
            razorpay_event_id_failed: eventId,
            acquirer_data: request.payload.payment.entity.acquirer_data,
            notes: request.payload.payment.entity.notes,
            additional_information: {
              error_code: request.payload.payment.entity.error_code,
              error_description:
                request.payload.payment.entity.error_description,
              error_reason: request.payload.payment.entity.error_reason,
              error_source: request.payload.payment.entity.error_source,
            },
            webhook_secret: webhookSecret,
            razorpay_payment_id: request.payload.payment.entity.id,
          },
          { where: { payment_id: razorPayOrder.payment_id } },
        ),
      ]);
      // to notify ERP about order status change
      const order = await SalesOrder.findByPk(razorPayOrder.order_id, {
        include: [
          SalesOrderAddress,
          SalesOrderPayment,
          SalesOrderAmountPromotion,
          {
            model: SalesOrderExtraInfo,
            attributes: [
              'registration_no',
              'exp_delivery_days',
              'exp_dispatch_days',
              'processed_at',
              'max_delivery_warehouse_code',
              'extra_info_entity_id',
            ],
          },
          {
            model: SalesOrderItem,
            include: [
              {
                model: OrderItemExtraInfo,
                attributes: ['referral_code', 'is_free_product', 'id'],
              },
              SalesOrderItemPromotion,
            ],
          },
        ],
      });
      this.notifyERP.notifyStatusUpdate(
        order,
        order.items,
        order.address,
        order.payment,
        request.payload.payment.entity.status,
        order.sales_order_extra_info,
      );
      return;
    } catch (error) {
      logger.error('failed payment webhook', error);
      return;
    }
  }

  /**
   * It updates order & payment details for captured payment
   * @param request PaymentWebhookRequestInterface
   * @param eventId unique razorpay header event id
   * @returns
   */
  async capturePayment(
    request: PaymentWebhookRequestInterface,
    eventId: string,
    razorPayOrder: SalesOrderPayment,
    webhookSecret: string,
  ) {
    try {
      const salesOrder = await SalesOrder.findByPk(razorPayOrder.order_id);
      if (
        !salesOrder ||
        salesOrder.status === OrderStatuses.PAYMENT_RECEIVED ||
        salesOrder.status !== OrderStatuses.PAYMENT_AUTHORIZE
      )
        return;
      await Promise.all([
        SalesOrder.update(
          {
            status: OrderStatuses.PAYMENT_RECEIVED,
          },
          {
            where: {
              order_id: razorPayOrder.order_id,
            },
          },
        ),
        SalesOrderPayment.update(
          {
            razorpay_event_id_capture: eventId,
            amount_paid: request.payload.payment.entity.amount,
            base_amount_paid: request.payload.payment.entity.amount,
            base_amount_paid_online: request.payload.payment.entity.amount,
            acquirer_data: request.payload.payment.entity.acquirer_data,
            notes: request.payload.payment.entity.notes,
            webhook_secret: webhookSecret,
            razorpay_payment_id: request.payload.payment.entity.id,
          },
          { where: { payment_id: razorPayOrder.payment_id } },
        ),
      ]);
      // to notify ERP about order status change
      const order = await SalesOrder.findByPk(razorPayOrder.order_id, {
        include: [
          SalesOrderAddress,
          SalesOrderPayment,
          SalesOrderAmountPromotion,
          {
            model: SalesOrderExtraInfo,
            attributes: [
              'registration_no',
              'exp_delivery_days',
              'exp_dispatch_days',
              'extra_info_entity_id',
              'processed_at',
              'max_delivery_warehouse_code',
            ],
          },
          {
            model: SalesOrderItem,
            include: [
              {
                model: OrderItemExtraInfo,
                attributes: ['referral_code', 'is_free_product', 'id'],
              },
              SalesOrderItemPromotion,
            ],
          },
        ],
      });
      this.notifyERP.notifyStatusUpdate(
        order,
        order.items,
        order.address,
        order.payment,
        request.payload.payment.entity.status,
        order.sales_order_extra_info,
      );
      return;
    } catch (error) {
      logger.error('capture payment webhook', error);
      return;
    }
  }

  /**
   * It will change order status in database based on razorpay status and notify erp
   * @param request ProcessOrderPayload
   * @returns preprocess order status and after process order status
   */
  async changeRequestOrderStatus(request: ProcessOrderPayload) {
    if (!request.status && !request.orderId)
      throw new BadRequestException(
        'Please provide orderId or status field in order to proceed',
      );

    let orders: SalesOrder[] = await this.getOrderStatusDetails(request);
    const errorDesc = [];
    if (orders.length === 0)
      return { message: 'No order data found', data: {} };

    const pre_process_orders = orders.map((o) => ({
      status: o.status,
      order_id: o.increment_id,
    }));

    for (const order of orders) {
      try {
        if (order.status === OrderStatuses.PAYMENT_AUTHORIZE) {
          const paymentDetails: RazorpayPaymentsFetchResponse =
            await this.razorPay.payments.fetch(
              order.payment.razorpay_payment_id,
            );
          if (paymentDetails.status === RazorpayPaymentStatuses.CAPTURED) {
            const rzpPayload: any = {
              payload: {
                payment: {
                  entity: {
                    id: order.payment.razorpay_payment_id,
                    order_id: order.payment.razorpay_order_id,
                    amount: order.payment.amount_paid,
                    notes: order.payment.notes,
                    acquirer_data: order.payment.acquirer_data,
                  },
                },
              },
            };
            await this.capturePayment(
              rzpPayload,
              order.payment.razorpay_event_id_authorize,
              order.payment,
              order.payment.webhook_secret,
            );
          }
        }
      } catch (e) {
        logger.error(`error in processing orders ${order.order_id}`, e);
        errorDesc.push({
          order_id: order.increment_id,
          error: e?.message || e,
        });
      }
    }

    orders = await this.getOrderStatusDetails({
      orderId: [...orders.map((o) => o.increment_id)],
      status: null,
    });

    const post_process_orders = orders.map((o) => ({
      status: o.status,
      order_id: o.increment_id,
    }));

    return {
      message: 'success',
      data: { pre_process_orders, post_process_orders, errorDesc },
    };
  }

  /**
   * It returns orders details array for particular status, order_id and combination of both
   * @param request ProcessOrderPayload
   * @returns array of orderDetails object
   */
  async getOrderStatusDetails(request: ProcessOrderPayload) {
    let orders: SalesOrder[] = [];
    if (request.status && request.orderId) {
      orders = await SalesOrder.findAll({
        where: {
          increment_id: {
            [Op.in]: [...request.orderId],
          },
          status: request.status,
        },
        limit: 5,
        include: [SalesOrderPayment],
      });
    } else if (request.orderId) {
      orders = await SalesOrder.findAll({
        where: {
          increment_id: {
            [Op.in]: [...request.orderId],
          },
        },
        limit: 5,
        include: [SalesOrderPayment],
      });
    } else {
      orders = await SalesOrder.findAll({
        where: {
          status: request.status,
        },
        limit: 5,
        include: [SalesOrderPayment],
      });
    }

    return orders;
  }

  /**
   * It returns array of orders with status for particular incrment_id
   * @param request FetchOrderStatusPayload
   * @returns array of orderDetails object
   */
  async getOrderStatusByIncrementId(request: FetchOrderStatusPayload) {
    const orders: SalesOrder[] = await SalesOrder.findAll({
      where: {
        increment_id: {
          [Op.in]: [...request.orderId],
        },
      },
    });

    if (orders.length === 0)
      return { message: 'No order data found', data: {} };
    return {
      message: 'success',
      data: {
        orders: [
          ...orders.map((o) => ({
            status: o.status,
            order_id: o.increment_id,
          })),
        ],
      },
    };
  }

  /**
   * fetch rzp payment info based on rzp_order_id and update status accordingly
   * @param orderId order increment id of type string
   * @returns array of orderDetails object
   */
  async updateDbStatusBasedOnRazorpay(orderId: string) {
    if (!orderId) throw new BadRequestException('OrderId is mandatory field');

    let post_process_order_info = null;

    const orderInfo: SalesOrder = await SalesOrder.findOne({
      where: { increment_id: orderId, status: OrderStatuses.PAYMENT_PENDING },
      include: [SalesOrderPayment],
    });

    if (!orderInfo)
      return {
        isError: false,
        messgae: 'No order data with pending status found for given order_id',
        data: null,
      };
    try {
      if (orderInfo?.payment?.razorpay_order_id) {
        const paymentDetails = await this.razorPay.orders.fetchPayments(
          orderInfo.payment.razorpay_order_id,
        );
        const capturedInfo: any = paymentDetails.items.find(
          (item: any) => item.status === RazorpayPaymentStatuses.CAPTURED,
        );
        if (
          capturedInfo &&
          capturedInfo.status === RazorpayPaymentStatuses.CAPTURED
        ) {
          await Promise.all([
            SalesOrder.update(
              {
                status: OrderStatuses.PAYMENT_RECEIVED,
              },
              {
                where: {
                  order_id: orderInfo.order_id,
                },
              },
            ),
            SalesOrderPayment.update(
              {
                amount_paid: capturedInfo.amount,
                base_amount_paid: capturedInfo.amount,
                base_amount_paid_online: capturedInfo.amount,
                acquirer_data: capturedInfo.acquirer_data,
                notes: capturedInfo.notes,
                webhook_secret: orderInfo.payment.webhook_secret,
                razorpay_payment_id: capturedInfo.id,
              },
              { where: { payment_id: orderInfo.payment.payment_id } },
            ),
          ]);

          // to notify ERP about order status change
          const order = await SalesOrder.findByPk(orderInfo.order_id, {
            include: [
              SalesOrderAddress,
              SalesOrderPayment,
              SalesOrderAmountPromotion,
              {
                model: SalesOrderExtraInfo,
                attributes: [
                  'registration_no',
                  'exp_delivery_days',
                  'exp_dispatch_days',
                  'processed_at',
                  'max_delivery_warehouse_code',
                  'extra_info_entity_id',
                ],
              },
              {
                model: SalesOrderItem,
                include: [
                  {
                    model: OrderItemExtraInfo,
                    attributes: ['referral_code', 'is_free_product', 'id'],
                  },
                  SalesOrderItemPromotion,
                ],
              },
            ],
          });

          this.notifyERP.notifyStatusUpdate(
            order,
            order.items,
            order.address,
            order.payment,
            null,
            order.sales_order_extra_info,
          );

          post_process_order_info = {
            status: order.status,
            order_id: order.increment_id,
          };
        }
      }
      return {
        isError: false,
        message: 'success',
        data: {
          pre_process_order_info: {
            status: orderInfo.status,
            order_id: orderInfo.increment_id,
          },
          post_process_order_info,
        },
      };
    } catch (e) {
      return { isError: true, messgae: e?.message, data: null };
    }
  }

  async publishOrderForMongoUpdate(request: FetchOrderStatusPayload) {
    try {
      const postProcessOrders = [];
      const orders = await SalesOrder.findAll({
        where: {
          increment_id: {
            [Op.in]: [...request.orderId],
          },
        },
        attributes: ['order_id', 'status'],
        limit: 10,
      });
      if (orders.length === 0)
        return {
          isError: false,
          messgae: 'No order data found for requested id.',
          data: null,
        };
      for (const orderInfo of orders) {
        if (orderInfo.status === OrderStatuses.PAYMENT_RECEIVED) {
          const order = await SalesOrder.findByPk(orderInfo.order_id, {
            include: [
              SalesOrderAddress,
              SalesOrderPayment,
              SalesOrderAmountPromotion,
              {
                model: SalesOrderExtraInfo,
                attributes: [
                  'registration_no',
                  'exp_delivery_days',
                  'exp_dispatch_days',
                  'processed_at',
                  'max_delivery_warehouse_code',
                  'extra_info_entity_id',
                ],
              },
              {
                model: SalesOrderItem,
                include: [
                  {
                    model: OrderItemExtraInfo,
                    attributes: ['referral_code', 'is_free_product', 'id'],
                  },
                  SalesOrderItemPromotion,
                ],
              },
            ],
          });
          await this.notifyERP.notifyStatusUpdate(
            order,
            order.items,
            order.address,
            order.payment,
            null,
            order.sales_order_extra_info,
          );
          postProcessOrders.push({
            order: order?.increment_id,
            status: order?.status,
            sync: true,
          });
        }
      }
      return {
        isError: false,
        messgae: 'success',
        data: {
          preProcessOrders: orders,
          postProcessOrders: postProcessOrders,
        },
      };
    } catch (e) {
      return { isError: true, messgae: e?.message, data: null };
    }
  }

  async publishOrder(request: PublishStatusPayload) {
    try {
      const postProcessOrders = [];
      const orders = await SalesOrder.findAll({
        where: {
          increment_id: {
            [Op.in]: [...request.orderId],
          },
        },
        attributes: ['order_id', 'status'],
        limit: 10,
      });
      if (orders.length === 0)
        return {
          isError: false,
          messgae: 'No order data found for requested id.',
          data: null,
        };
      for (const orderInfo of orders) {
        const order = await SalesOrder.findByPk(orderInfo.order_id, {
          include: [
            SalesOrderAddress,
            SalesOrderAmountPromotion,
            SalesOrderPayment,
            {
              model: SalesOrderExtraInfo,
              attributes: [
                'registration_no',
                'exp_delivery_days',
                'exp_dispatch_days',
                'processed_at',
                'max_delivery_warehouse_code',
                'extra_info_entity_id',
              ],
            },
            {
              model: SalesOrderItem,
              include: [
                {
                  model: OrderItemExtraInfo,
                  attributes: ['referral_code', 'is_free_product', 'id'],
                },
                SalesOrderItemPromotion,
              ],
            },
          ],
        });
        await this.notifyERP.notifyStatusUpdate(
          order,
          order.items,
          order.address,
          order.payment,
          null,
          order.sales_order_extra_info,
          request?.email_notification || false,
        );
        postProcessOrders.push({
          order: order?.increment_id,
          status: order?.status,
          sync: true,
        });
      }
      return {
        isError: false,
        messgae: 'success',
        data: {
          preProcessOrders: orders,
          postProcessOrders: postProcessOrders,
        },
      };
    } catch (e) {
      return { isError: true, messgae: e?.message, data: null };
    }
  }

  async handleMannualCaptureError(rzpPaymentId: string, rzpCaptureError: any) {
    try {
      if (rzpCaptureError?.error?.description) {
        if (
          rzpCaptureError.error.description.includes(
            'This payment has already been captured',
          )
        ) {
          const paymentDetails: RazorpayPaymentsFetchResponse =
            await this.razorPay.payments.fetch(rzpPaymentId);
          if (paymentDetails.status === RazorpayPaymentStatuses.CAPTURED) {
            return true;
          }
        }
        return false;
      }
      return false;
    } catch (e) {
      logger.info(`Error in handleMannualCaptureError: ${JSON.stringify(e)}`);
      return false;
    }
  }
}
