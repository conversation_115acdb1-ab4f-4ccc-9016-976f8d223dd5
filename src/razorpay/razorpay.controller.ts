import { Body, Controller, Headers, Post, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaymentWebhookRequestInterface } from 'src/interface/webhook-request';
import { WebhookGuard } from 'src/guard/webhook-secret-guard';
import { RazorpayService } from './razorpay.service';
import { ResponseDto } from 'src/dto/process-request-response.dto';
import { ProcessOrderPayload } from 'src/dto/process-order-request.dto';
import { FetchOrderStatusPayload } from 'src/dto/fetch-order-request.dto';
import { PublishGuard } from 'src/guard/publish-order-guard';
import { PublishStatusPayload } from '../dto/publish-order-request-dto';

@Controller('razorpay')
@ApiTags('RazorPay')
export class RazorpayController {
  constructor(private readonly razorpayService: RazorpayService) {}

  @UseGuards(WebhookGuard)
  @ApiOperation({
    summary: 'Webhook to be called by razorpay on payment authorize',
  })
  @ApiOkResponse({
    description: 'success',
  })
  @Post('webhook')
  async paymentWebhook(
    @Body() request: PaymentWebhookRequestInterface,
    @Headers('x-razorpay-event-id') eventId: string,
    @Headers('x-razorpay-signature') webhookSignature: string,
  ) {
    await this.razorpayService.paymentWebhook(
      request,
      eventId,
      webhookSignature,
    );
    return 'OK';
  }

  @ApiOperation({
    summary: 'api endpoint to process orders and update status',
  })
  @ApiOkResponse({ description: 'success', type: ResponseDto })
  @Post('/changeOrderStatus')
  async processRequestOrders(@Body() request: ProcessOrderPayload) {
    return await this.razorpayService.changeRequestOrderStatus(request);
  }

  @ApiOperation({
    summary: 'api endpoint to fetch order status',
  })
  @ApiOkResponse({ description: 'success', type: ResponseDto })
  @Post('/fetchOrderStatus')
  async fetchOrderStatus(@Body() request: FetchOrderStatusPayload) {
    return await this.razorpayService.getOrderStatusByIncrementId(request);
  }

  @ApiOperation({
    summary: 'api endpoint to update and db status',
  })
  @ApiOkResponse({ description: 'success', type: ResponseDto })
  @Post('/updateDbStatusbasedOnRazorpay')
  async updateDbStatusBasedOnRazorpay(@Body() request: { orderId: string }) {
    return await this.razorpayService.updateDbStatusBasedOnRazorpay(
      request.orderId,
    );
  }

  @ApiOperation({
    summary: 'api endpoint to update mongo order status',
  })
  @ApiOkResponse({ description: 'success', type: ResponseDto })
  @Post('/updateMongoDbOrderStatus')
  async publishOrderForMongoUpdate(@Body() request: FetchOrderStatusPayload) {
    return await this.razorpayService.publishOrderForMongoUpdate(request);
  }

  @UseGuards(PublishGuard)
  @ApiOperation({
    summary: 'api endpoint to update mongo',
  })
  @ApiOkResponse({ description: 'success', type: ResponseDto })
  @Post('/publishorder')
  async publishOrder(@Body() request: PublishStatusPayload) {
    return await this.razorpayService.publishOrder(request);
  }
}
