import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import Razorpay = require('razorpay');
import config from 'src/config/env';
import { SalesOrderPayment } from 'src/database/entities/sales-order-payment';
import { Op } from 'sequelize';
import { logger } from 'src/utils/service-logger';

@Injectable()
export class WebhookGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<any> {
    const request = context.switchToHttp().getRequest();
    const webhookSignature = request.headers['x-razorpay-signature'];
    // validate webhook signature (if received)
    if (webhookSignature) {
      const isValid = await Razorpay.validateWebhookSignature(
        JSON.stringify(request.body),
        webhookSignature,
        config.razorpay.api_secret,
      );
      if (!isValid) {
        logger.error(`Webhook doesn't match`, {
          headers: request.headers,
          body: request.body,
        });
        return true;
      }
    }

    // validate duplicate webhook
    const eventId = request.headers['x-razorpay-event-id'];
    const eventExists = await SalesOrderPayment.findOne({
      where: {
        [Op.or]: [
          { razorpay_event_id_authorize: eventId },
          { razorpay_event_id_capture: eventId },
          { razorpay_event_id_failed: eventId },
        ],
      },
    });
    if (!eventExists) return true;

    logger.error('Redundant razorpay event', {
      headers: request.headers,
      body: request.body,
    });
    return true;
  }
}
