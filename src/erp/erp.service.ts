import { BadRequestException, Injectable } from '@nestjs/common';
import { find } from 'lodash';
import { SalesOrder } from 'src/database/entities/sales-order';
import { SalesOrderItem } from 'src/database/entities/sales-order-item';
import {
  ERPOrderItemUpdateDto,
  ItemUpdateProductDto,
} from './dto/erp-item-update-request';
import { ERPOrderUpdateRequestDto } from './dto/erp-order-update-request';
import { Op } from 'sequelize';

@Injectable()
export class ErpService {
  /**
   * It updates existing order's status based on ERP action
   * @param request ErpOrderUpdateRequestDto
   * @returns
   */
  async updateOrderStatus(request: ERPOrderUpdateRequestDto) {
    const orders = await SalesOrder.findAll({
      where: { order_id: { [Op.in]: request.orders.map((o) => +o.orderId) } },
    });
    if (!orders?.length)
      throw new BadRequestException('Requested orders does not exist');
    for (const order of orders) {
      const newStatus = request.orders.find(
        (o) => +o.orderId === order.order_id,
      )?.orderStatus;
      if (!newStatus || order.status === newStatus) continue;
      await SalesOrder.update(
        { status: newStatus },
        { where: { order_id: order.order_id } },
      );
    }

    return { message: 'success', data: {} };
  }

  /**
   * It updates order-item's quantity details
   * @param request ERPOrderItemUpdateDto
   * @returns
   */
  async updateItemDetails(request: ERPOrderItemUpdateDto) {
    for (const obj of request.items) {
      await this.fetchAndUpdateItem(obj.orderId, obj.products);
    }

    return { message: 'success', data: {} };
  }

  /**
   * It validate & update quantity related details for each product
   * @param orderId Unique order-id to validate items
   * @param products ItemUpdateProductDto[]
   */
  async fetchAndUpdateItem(orderId: string, products: ItemUpdateProductDto[]) {
    const order = await SalesOrder.findByPk(orderId, {
      include: [SalesOrderItem],
    });
    if (!order) throw new BadRequestException('Invalid order id');
    for (const obj of products) {
      if (!find(order.items, { order_item_id: +obj.product_id })) continue;
      await SalesOrderItem.update(
        {
          qty_backordered: obj.qty_backordered,
          qty_canceled: obj.qty_canceled,
          qty_invoiced: obj.qty_invoiced,
          qty_refunded: obj.qty_refunded,
          qty_shipped: obj.qty_shipped,
        },
        { where: { order_item_id: obj.product_id } },
      );
    }
  }
}
