import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  ValidateNested,
  IsArray,
  IsNumberString,
} from 'class-validator';
import { OrderStatuses } from 'src/config/constants';

export class OrderUpdateRequestDto {
  @ApiProperty({ description: 'Unique id of order' })
  @IsNumberString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Status to set',
    enum: Object.values(OrderStatuses),
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(Object.values(OrderStatuses))
  orderStatus: string;
}

export class ERPOrderUpdateRequestDto {
  @ApiProperty({ type: OrderUpdateRequestDto, isArray: true })
  @IsArray()
  @ValidateNested()
  @Type(() => OrderUpdateRequestDto)
  orders: OrderUpdateRequestDto[];
}
