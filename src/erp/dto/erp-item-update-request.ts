import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsString, ValidateNested } from 'class-validator';

export class ItemUpdateProductDto {
  @ApiProperty()
  @IsString()
  product_id: string;

  @ApiProperty()
  @IsNumber()
  qty_backordered: number;

  @ApiProperty()
  @IsNumber()
  qty_canceled: number;

  @ApiProperty()
  @IsNumber()
  qty_invoiced: number;

  @ApiProperty()
  @IsNumber()
  qty_ordered: number;

  @ApiProperty()
  @IsNumber()
  qty_refunded: number;

  @ApiProperty()
  @IsNumber()
  qty_shipped: number;
}

export class ERPItemUpdateDto {
  @ApiProperty()
  @IsString()
  orderId: string;

  @ApiProperty({ type: ItemUpdateProductDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => ItemUpdateProductDto)
  products: ItemUpdateProductDto[];
}

export class ERPOrderItemUpdateDto {
  @ApiProperty({ type: ERPItemUpdateDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => ERPItemUpdateDto)
  items: ERPItemUpdateDto[];
}
