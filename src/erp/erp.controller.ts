import {
  Body,
  Controller,
  Post,
  UseFilters,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ERPOrderUpdateRequestDto } from './dto/erp-order-update-request';
import { ErpService } from './erp.service';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ResponseInterceptor } from 'src/dispatcher/response.interceptor';
import { AllExceptionsFilter } from 'src/dispatcher/exception.filter';
import { ResponseDto } from './dto/response';
import { ERPOrderItemUpdateDto } from './dto/erp-item-update-request';

@ApiTags()
@Controller('erp')
@UsePipes(new ValidationPipe())
@UseInterceptors(ResponseInterceptor)
@UseFilters(AllExceptionsFilter)
export class ErpController {
  constructor(private readonly erpService: ErpService) {}

  @ApiOperation({ summary: 'To update order status' })
  @ApiOkResponse({ description: 'success', type: ResponseDto })
  @Post('order-status')
  async updateOrderStatus(@Body() request: ERPOrderUpdateRequestDto) {
    return await this.erpService.updateOrderStatus(request);
  }

  @ApiOperation({ summary: 'To update order item details' })
  @ApiOkResponse({ description: 'success', type: ResponseDto })
  @Post('order-item')
  async updateOrderItem(@Body() request: ERPOrderItemUpdateDto) {
    return await this.erpService.updateItemDetails(request);
  }
}
