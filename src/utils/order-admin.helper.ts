import { Injectable } from '@nestjs/common';
import { CartPaymentInfoResponse } from 'src/interface/cart-external-response';
import config from '../config/env';
import { ExternalServiceCaller } from './external-service-caller';

@Injectable()
export class OrderAdminHelper {
  constructor(private readonly externalServiceCaller: ExternalServiceCaller) {}

  async getAdminCart(customer_id: number, cart_id: string) {
    const headers = {};
    const url = `${config.dk.get_admin_cart_url}/${customer_id}/${cart_id}?get_order_res=true`;

    const cartDetails = await this.externalServiceCaller.get(url, headers);
    return cartDetails;
  }

  async getPaymentAndShippingDetails(
    cartId: string,
    customer_id: number,
  ): Promise<CartPaymentInfoResponse> {
    let headers = {};
    const url: string = `${config.dk.get_admin_payment_info}/${customer_id}/${cartId}`;
    const paymentAndShippingData = await this.externalServiceCaller.get(
      url,
      headers,
    );
    return paymentAndShippingData;
  }
}
