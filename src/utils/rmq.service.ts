import { Injectable, Logger } from '@nestjs/common';
import { connect } from 'amqplib';
import { consumerOptions } from '../interface/consumer-options';
import { publishOptions } from 'src/interface/publish-options';
import config from 'src/config/env';

@Injectable()
export class RmqService {
  private readonly logger = new Logger(RmqService.name);
  publisherChannelconn = null;
  consumerChannelconn = null;
  publisherChannel = null;
  consumerChannel = null;
  connCloseCount = 0;

  async createChannel(_type: string) {
    try {
      this[`${_type}conn`] = await connect(
        `amqps://${config.RMQ.user}:${encodeURIComponent(
          config.RMQ.password,
        )}@${config.RMQ.host}`,
      );
      this[_type] = await this[`${_type}conn`].createChannel();

      this.logger.log(`${_type} connected successfully.`);
    } catch (e) {
      this.logger.error(`Error createChannel`, e);
    }
  }

  async publishMessgae(publishOptions: publishOptions) {
    try {
      if (!this.publisherChannel) {
        await this.createChannel('publisherChannel');
      }
      await this.publisherChannel.assertExchange(
        publishOptions.exchangeName,
        publishOptions.exchangeType,
      );
      await this.publisherChannel.publish(
        publishOptions.exchangeName,
        publishOptions.routingKey,
        Buffer.from(
          JSON.stringify({
            data: publishOptions.data,
          }),
        ),
      );
      this.logger.log(
        `${JSON.stringify(publishOptions.data)} sent to exchange ${
          publishOptions.exchangeName
        } successfully`,
      );
      await this.disConnect('publisherChannel');
    } catch (e) {
      await this.disConnect('publisherChannel');
      this.logger.error(` error in publish service`, e);
    }
  }

  async consumer(
    queueOptions: consumerOptions,
    processCallback: any,
    channel?: null,
  ) {
    try {
      this.consumerChannel = channel;
      if (!this.consumerChannel) {
        await this.createChannel('consumerChannel');
      }
      await this.consumerChannel.assertExchange(
        queueOptions.exchangeName,
        queueOptions.exchangeType,
      );
      const q = await this.consumerChannel.assertQueue(queueOptions.queue, {
        durable: true,
      });
      await this.consumerChannel.bindQueue(
        q.queue,
        queueOptions.exchangeName,
        queueOptions.routingKey,
      );
      await this.consumerChannel.prefetch(10);
      this.consumerChannel.consume(
        q.queue,
        async (msg: any) => {
          if (msg !== null) {
            try {
              const data = msg?.content.toString();
              await processCallback(data);
              this.consumerChannel.ack(msg);
            } catch (e) {
              //this.consumerChannel.reject(msg);
              this.logger.log(
                `error in cancel order consumer msg-${queueOptions.routingKey}`,
                msg?.content.toString(),
              );
              this.logger.error('error in consume function', e);
            }
          }
        },
        { noAck: false },
      );

      this.consumerChannel.on('close', async () => {
        try {
          this.logger.log(
            `consumerChannel on closeEvent ${new Date().toString()}`,
          );
          channel = await this.consumerChannelconn.createChannel();
          await this.consumer(queueOptions, processCallback, channel);
        } catch (e) {
          this.logger.log(
            `error in consumerChannel on close event ${new Date().toString()} `,
          );
        }
      });

      this.consumerChannelconn.on('close', async () => {
        try {
          this.consumerChannel = null;
          await this.consumer(queueOptions, processCallback);
        } catch (e) {
          this.logger.log(
            `consumerChannelconn closed ${new Date().toString()}`,
          );
        }
      });

      this.consumerChannel.on('error', async () => {
        try {
          this.logger.log(`consumerChannelError ${new Date().toString()} `);
          channel = await this.consumerChannelconn.createChannel();
          await this.consumer(queueOptions, processCallback, channel);
        } catch (e) {
          this.logger.log(
            `error in Channel reconnection ${new Date().toString()} `,
          );
        }
      });

      //add retry in case of consumer close
      this.consumerChannelconn.on('error', async () => {
        this.logger.log(
          `consumerChannelconnerror = ${++this
            .connCloseCount} at ${new Date().toString()}`,
        );
        this.consumerChannel = null;
        await this.consumer(queueOptions, processCallback);
      });
    } catch (e) {
      //await this.disConnect('consumerChannel');
      this.logger.error(` error in consumer service`, e);
    }
  }

  async disConnect(_type: string) {
    try {
      //await this[_type]?.close();
      this[_type] = null;
      await this[`${_type}conn`]?.close();
    } catch (e) {
      this.logger.error(`error in disconnect ${_type} fun`, e);
    }
  }
}
