import { readFileSync } from 'fs';
import config from 'src/config/env';
import { ExternalServiceCaller } from './external-service-caller';
import * as ejs from 'ejs';
import { Injectable } from '@nestjs/common';
import { logger } from './service-logger';
import { SalesOrderAddress } from 'src/database/entities/sales-order-address';
import { SalesOrder } from 'src/database/entities/sales-order';
import { SalesOrderItem } from 'src/database/entities/sales-order-item';
import { OrderStatuses, NOTIFICATION_EVENTS } from 'src/config/constants';
import { sumBy } from 'lodash';
import { whatsappNotificationData } from 'src/interface/whatsapp-notification-data';
import { OrderHelper } from './order.helper';

@Injectable()
export class NotificationHelper {
  constructor(
    private readonly externalServiceCaller: ExternalServiceCaller,
    private readonly orderHelper: OrderHelper,
  ) {}

  /**
   * To send order confirmation email to user
   * @param recipient user email id
   * @param itemsCount total order items
   * @param total total bill
   * @param userName user's first name
   * @returns
   */
  async sendOrderPlacedMail(
    payment_method: string,
    order: SalesOrder,
    orderItems: SalesOrderItem[],
    orderAddresses: SalesOrderAddress[],
  ) {
    try {
      const itemsData = [];

      for (const item of orderItems) {
        itemsData.push({
          productName: item?.product_name,
          quantity: item?.qty_ordered,
          price: item?.price_incl_tax,
          is_free_product: false,
        });

        if (item.salesOrderItemPromotions?.length) {
          for (const prom of item.salesOrderItemPromotions) {
            itemsData.push({
              productName: prom.meta_info?.name,
              quantity: prom.qty,
              price: prom.meta_info.price,
              is_free_product: true,
            });
          }
        }
      }

      if (order.salesOrderAmountPromotions?.length) {
        for (const prom of order.salesOrderAmountPromotions) {
          itemsData.push({
            productName: prom.meta_info?.name,
            quantity: prom.qty,
            price: prom.meta_info.price,
            is_free_product: true,
          });
        }
      }

      const statusMessages = {
        new_order: 'confirmed',
        payment_received: 'confirmed',
      };

      const { earnedPoints } = orderItems.reduce<{
        earnedPoints: number;
      }>(
        (acc, item) => ({
          earnedPoints:
            acc.earnedPoints + item.reward_points * item.qty_ordered,
        }),
        {
          earnedPoints: 0,
        },
      );

      let templatePath = `src/template/order-template.html`;

      if (
        [
          OrderStatuses.CANCELLED,
          OrderStatuses.PAYMENT_RECEIVED,
          OrderStatuses.NEW_ORDER,
        ].indexOf(order.status) !== -1
      ) {
        templatePath = `src/template/updated-order-template.html`;
      }

      const headers = {
        authorization: config.notification.mailer_x_api_key,
      };
      const template = ejs.render(readFileSync(templatePath).toString(), {
        viewCartUrl: config.dk.frontend_view_cart_url,
        status:
          order.status === OrderStatuses.PAYMENT_PENDING
            ? 'failed'
            : order.status,
        userName: order?.customer_firstname ?? null,
        orderId: order?.increment_id ?? null,
        orderDate: new Date(order.createdAt)
          .toLocaleString('en-IN', {
            timeZone: 'IST',
            dateStyle: 'long',
            timeStyle: 'medium',
          })
          .replace(' at', ''),
        cancelUpdateDate: new Date(order.updatedAt)
          .toLocaleString('en-IN', {
            timeZone: 'IST',
            dateStyle: 'long',
            timeStyle: 'medium',
          })
          .replace(' at', ''),
        billingAddress: orderAddresses?.find(
          (data) => data.address_type === 'billing',
        ),
        shippingAddress: orderAddresses?.find(
          (data) => data.address_type === 'shipping',
        ),
        paymentMethod: payment_method,
        items: itemsData,
        subTotal: +order.subtotal_incl_tax,
        couponDiscount: +order?.discount_amount || 0,
        shippingCharge: +order?.shipping_amount || 0,
        handlingFee: +order?.handling_fee || 0,
        grandTotal: +order.grand_total,
        rewardDiscount: +order.rewards_discount || 0,
        earnedPoints: earnedPoints || 0,
      });

      const data = {
        Detail: {
          entity_value: order.customer_email,
          source: 'order_payment',
          event_type: NOTIFICATION_EVENTS?.[order.status] ?? '',
          subject: `Your dentalkart order  #${order.increment_id} is ${
            statusMessages[order.status] || order.status
          }`,
          body: template,
          from: config.notification.mailer_username,
          'detail-type': 'email-notifications',
        },
        DetailType: 'notifications-event',
        Source: 'dentalkart.notifications.service',
      };

      if (
        [OrderStatuses.PAYMENT_RECEIVED, OrderStatuses.NEW_ORDER].indexOf(
          order.status,
        ) !== -1
      ) {
        data['Detail']['bcc_email_id'] = config.notification.bcc_mail_id;
      }

      const body = JSON.stringify(data);

      // logger.info('Mail sender request', { body });

      await this.externalServiceCaller.post(
        config.notification.mailer_url,
        headers,
        body,
      );

      return;
    } catch (error) {
      logger.error('Error in sending place order mail', error);
    }
  }

  async sendWhatsAppNotification(payload: whatsappNotificationData) {
    const url = config.notification.whatsapp_url;
    const body = {
      Detail: {
        data: {
          ...payload,
        },
      },
      DetailType: 'Send Whatsapp notificaion',
      Source: 'dentalkart.whatsapp.notification',
    };
    await this.externalServiceCaller.post(
      url,
      { 'Content-Type': 'application/json' },
      body,
    );
  }
}
