import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ExternalServiceCaller } from './external-service-caller';
import { NotificationHelper } from './notification.helper';
import { NotifyERP } from './notify-erp';
import { OrderHelper } from './order.helper';
import { RmqService } from './rmq.service';

@Module({
  imports: [HttpModule.register({ maxRedirects: 5, timeout: 10000 })],
  providers: [
    ExternalServiceCaller,
    OrderHelper,
    NotificationHelper,
    NotifyERP,
    RmqService,
  ],
  exports: [HttpModule, UtilsModule, NotifyERP, RmqService, OrderHelper],
})
export class UtilsModule {}
