import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { logger } from './service-logger';

@Injectable()
export class ExternalServiceCaller {
  constructor(private readonly httpService: HttpService) {}

  /**
   * Make a call to any GET method type API with specified headers
   * @param url API end url
   * @param headers header object
   * @returns
   */
  get = async (
    url: string,
    headers: { [key: string]: string },
  ): Promise<any> => {
    try {
      const response = await firstValueFrom(
        this.httpService.get(url, { headers }),
      );
      // logger.info('get call response', response?.data);
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      logger.error('Error in get call', error);
      return null;
    }
  };

  /**
   * Make a call to any POST method type API with specified headers
   * and requested payload data
   * @param url API end url
   * @param headers header object
   * @param payload request body
   * @returns
   */
  post = async (
    url: string,
    headers: { [key: string]: string },
    payload: any,
  ): Promise<any> => {
    try {
      const response = await firstValueFrom(
        this.httpService.post(url, payload, { headers }),
      );
      // logger.info('post call response', response?.data);
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      logger.error('Error in post call', error);
    }
  };

  /**
   * It makes a PUT method type call to an external API
   * @param url API end url
   * @param headers header object
   * @param payload request body
   * @returns
   */
  put = async (
    url: string,
    headers: { [key: string]: string },
    payload?: any,
  ): Promise<any> => {
    try {
      const response = await firstValueFrom(
        this.httpService.put(url, payload || {}, { headers }),
      );
      // logger.info('put call response', response?.data);
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      logger.error('Error in put call', error);
    }
  };
}
