import * as winston from 'winston';
import * as dailyRotateFile from 'winston-daily-rotate-file';
const { combine, timestamp, json } = winston.format;

/**
 * It creates a logger used for information & error tracking
 */
export const logger = winston.createLogger({
  format: combine(timestamp(), json()),
  exitOnError: false,
  transports: [
    new winston.transports.Console(),
    new dailyRotateFile({ filename: 'order-service', level: 'info' }),
  ],
  // to log unhandled errors
  exceptionHandlers: [
    new winston.transports.Console(),
    new dailyRotateFile({
      filename: 'order-service-error',
      level: 'error',
      format: winston.format.errors({ stack: true }),
    }),
  ],
});
