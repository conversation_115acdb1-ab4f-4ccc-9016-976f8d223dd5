import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import config from './config/env';
import { ErpModule } from './erp/erp.module';
import { logger } from './utils/service-logger';
import { ValidationPipe } from './utils/validation.pipe';
import { RazorpayModule } from './razorpay/razorpay.module';
import { RequestMethod } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const appPort = config.app.port;

  app.enableCors({ origin: '*', allowedHeaders: '*', methods: '*' });
  app.useGlobalPipes(new ValidationPipe());
  app.setGlobalPrefix('/api/v1', {
    exclude: [{ path: 'order/api/v1/orders(.*)', method: RequestMethod.ALL }],
  });

  const swaggerOptions = new DocumentBuilder()
    .setTitle('Order service')
    .setDescription('Order Sevice APIs')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, swaggerOptions, {
    include: [AppModule, ErpModule, RazorpayModule],
  });
  SwaggerModule.setup('api-docs', app, document);

  await app.listen(appPort);
  logger.info('Order microservice started at port :' + appPort);
}
bootstrap();
