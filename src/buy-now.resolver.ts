import {
  BadRequestException,
  Inject,
  UnauthorizedException,
} from '@nestjs/common';
import { Resolver, Mutation, Args, Context, Query } from '@nestjs/graphql';
import { SalesOrder } from './database/entities/sales-order';
import { SalesOrderAddress } from './database/entities/sales-order-address';
import { SalesOrderItem } from './database/entities/sales-order-item';
import { SalesOrderPayment } from './database/entities/sales-order-payment';
import { SalesOrderTax } from './database/entities/sales-order-tax';
import { PlaceOrderRequestInterface } from './interface/place-order-request';
import { OrderMapper } from './mapper/order';
import { OrderHelper } from './utils/order.helper';
import { Sequelize } from 'sequelize-typescript';
import { SalesOrderTaxItem } from './database/entities/sales-order-tax-item';
import { PaymentMethods } from './config/constants';
import { find } from 'lodash';
import { NotifyERP } from './utils/notify-erp';
import { RazorpayService } from './razorpay/razorpay.service';
import { SalesOrderExtraInfo } from './database/entities/sales-order-extra-info';
import { OrderItemExtraInfo } from './database/entities/order-item-extra-info';
import { SalesOrderItemPromotion } from './database/entities/sales-order-item-promotion';
import { SalesOrderAmountPromotion } from './database/entities/sales-order-amount-promotion';

@Resolver('BuyNowOrder')
export class BuyNowPlaceOrder {
  constructor(
    private readonly orderHelper: OrderHelper,
    private readonly orderMapper: OrderMapper,
    private readonly notifyERP: NotifyERP,
    private readonly razorpayService: RazorpayService,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}

  @Mutation('buyNowPlaceOrder')
  async palceOrderOnBuyNowCart(
    @Args() request: { input: PlaceOrderRequestInterface },
    @Context() context: any,
  ) {
    const authToken = this.orderHelper.getUserToken(
      context.req.headers['authorization'],
    );

    if (!authToken) {
      throw new UnauthorizedException('Unauthorized user');
    }

    const platform = context.req.headers?.['platform'] ?? '';
    const version = context.req.headers?.['version'] ?? null;

    const cartAndCartPayment =
      await this.orderHelper.fetchBuyNowCartPaymentInfo(
        request.input.cart_id,
        authToken,
      );

    const { cart_info: cartExists, payment_info: cartPayment } =
      cartAndCartPayment;

    if (!cartExists || !cartExists.items) {
      return new BadRequestException('Invalid cartId');
    }

    // validate payment_method received in request
    if (
      cartPayment.payment_methods
        .map((o) => o.code)
        .indexOf(request.input.payment_method) === -1
    ) {
      return new BadRequestException('Invalid payment method');
    }

    const skus = this.orderHelper.extractAllSkus(cartExists);

    const products = await this.orderHelper.getProductDataFromSku(
      skus,
      cartExists?.extension_attributes?.shipping_assignments?.[0]?.shipping
        ?.address?.country_id || cartExists?.billing_address?.country_id,
    );

    const responseData = {
      amount: this.orderHelper.roundAndToBaseCurrencyAmount(
        Number(
          find(cartPayment.totals.total_segments, { code: 'grand_total' })
            ?.value,
        ) || 0,
        2,
        100,
      ),
      currency: cartExists.currency.quote_currency_code,
      merchant_id: '',
      order_number: '',
      payment_method: request.input.payment_method,
      reference_number: '',
    };

    const orderObj = await this.orderMapper.buildSalesOrderObj(
      cartExists,
      cartPayment,
      context?.req?.ip || null,
      request.input.payment_method,
      platform,
      version,
    );

    const t1 = await this.sequelize.transaction();

    try {
      const salesOrderObj = await SalesOrder.create(orderObj, {
        transaction: t1,
      });

      const { billing_address, shipping_address } =
        this.orderMapper.buildSalesOrderAddressObj(
          cartExists,
          salesOrderObj.order_id,
        );

      //validate billing shipping address
      this.orderHelper.validateOrderAdddress(
        billing_address,
        request.input.cart_id,
      );
      if (shipping_address) {
        this.orderHelper.validateOrderAdddress(
          shipping_address,
          request.input.cart_id,
        );
      }

      const salesOrderAddresses = await Promise.all([
        await SalesOrderAddress.create(billing_address, {
          transaction: t1,
        }),
        shipping_address &&
          (await SalesOrderAddress.create(shipping_address, {
            transaction: t1,
          })),
      ]);

      const orderTaxObj = this.orderMapper.buildSalesOrderTaxObj(
        cartPayment,
        salesOrderObj.order_id,
      );
      const salesTaxObj = await SalesOrderTax.create(orderTaxObj, {
        transaction: t1,
      });
      const orderItemObj = this.orderMapper.buildSalesOrderItemObj(
        cartExists,
        cartPayment,
        salesOrderObj.order_id,
        products,
        salesTaxObj.tax_id,
      );
      const salesOrderItems = await SalesOrderItem.bulkCreate(orderItemObj, {
        include: [
          { model: SalesOrderTaxItem, as: 'tax' },
          { model: OrderItemExtraInfo, as: 'itemExtraInfo' },
          { model: SalesOrderItemPromotion, as: 'salesOrderItemPromotions' },
        ],
        transaction: t1,
      });

      // Map and create cart amount promotions if they exist
      const amountPromotions = this.orderMapper.mapAmountPromotions(
        cartExists,
        salesOrderObj.order_id,
        products,
      );

      if (amountPromotions.length > 0) {
        salesOrderObj.salesOrderAmountPromotions =
          await SalesOrderAmountPromotion.bulkCreate(amountPromotions, {
            transaction: t1,
          });
      }

      let razorpayOrder;
      if (request.input.payment_method === PaymentMethods.RAZOR_PAY) {
        razorpayOrder = await this.razorpayService.createOrder({
          amount: this.orderHelper.roundAndToBaseCurrencyAmount(
            Number(salesOrderObj.grand_total) || 0,
            2,
            100,
          ),
          currency: salesOrderObj.order_currency_code,
          paymentId: salesOrderObj.order_id,
          orderId: salesOrderObj.increment_id,
        });
        responseData.merchant_id = razorpayOrder.merchantId;
        responseData.reference_number = razorpayOrder.referenceNumber;
      }
      const orderPaymentObj = this.orderMapper.buildSalesOrderPaymentObj(
        cartPayment,
        salesOrderObj.order_id,
        request.input.payment_method,
        razorpayOrder,
      );
      const salesOrderPayment = await SalesOrderPayment.create(
        orderPaymentObj,
        { transaction: t1 },
      );

      // build and save sales_order_extra_info values
      let sales_order_extra_info = null;
      if (request?.input?.extension_attributes?.length) {
        const sales_order_extra_info_obj =
          this.orderMapper.buildSalesExtraInfoObject(
            salesOrderObj.order_id,
            request.input.extension_attributes,
          );
        sales_order_extra_info = await SalesOrderExtraInfo.create(
          sales_order_extra_info_obj,
          {
            transaction: t1,
          },
        );
      }

      await this.orderHelper.inactiveBuyNowCart(request.input.cart_id);

      await t1.commit();
      responseData.order_number = salesOrderObj.increment_id;

      const isFirstOrder = await this.orderHelper.isFirstOrder(
        salesOrderObj.customer_id,
      );

      // Notify ERP about order creation
      this.notifyERP.notifyStatusUpdate(
        salesOrderObj,
        salesOrderItems,
        salesOrderAddresses,
        salesOrderPayment,
        null,
        sales_order_extra_info,
        true,
        isFirstOrder,
      );
      //Update coupon usgae api call
      this.orderHelper.updateRuleUsage(salesOrderObj);
    } catch (error) {
      await t1.rollback();
      console.log('Error in BuyNowPlaceOrder', error?.message || error);
      this.orderHelper.handleServiceError(
        error,
        `Error Buy now resolver order for cart ID ${request?.input?.cart_id}`,
      );
    }

    return responseData;
  }
}
