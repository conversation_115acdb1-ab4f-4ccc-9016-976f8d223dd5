interface Pagination {
  next_page: number;
  page_size: number;
  page_no: number;
}

interface Currency {
  code: string;
  symbol: string;
}

interface Dates {
  ordered: {
    date: Date;
    text: string;
  };
  delivered: {
    date: Date;
    text: string;
  };
}

interface Rewards {
  earned_points: number;
  spent_points: number;
  reward_term: string;
  reward_icon: string;
}

interface OrderSummary {
  order_amount: number;
  unique_product_count: number;
  total_product_qty: number;
  total_savings: number;
}

interface Status {
  id: number | null;
  code: string;
  label: string;
}

interface StatusItem {
  id: number | null;
  code: string;
  label: string;
}

interface RateInfo {
  rated_count: number;
  total_count: number;
  is_full_rated: boolean;
}

interface Item {
  name: string;
  price: number;
  ordered_qty: number;
  row_total: number;
  status: StatusItem;
  product_id: string;
  sku: string;
  thumbnail_url: string;
  url_key: string;
  reward_earned_coins: number;
  is_reviewed: boolean;
}

interface Order {
  data: {
    dates: Dates;
    order_id: string;
    rewards: Rewards;
    can_cancel: boolean;
    can_return: boolean;
    can_reorder: boolean;
    is_processing: boolean;
    order_summary: OrderSummary;
    status: Status;
    cta_title: string;
    item_text: string;
    items: Item[];
    rate_info: RateInfo;
  };
  tracking: {
    tracking_available: boolean;
    track_title: string;
    deeplink: string;
  };
}

export interface CustomerOrdersResponse {
  pagination: Pagination;
  total_order_count: number;
  has_more_orders: boolean;
  currency: Currency;
  orders: Order[];
}
