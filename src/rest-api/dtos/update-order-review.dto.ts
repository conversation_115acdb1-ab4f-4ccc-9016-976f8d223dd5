import {
  IsNotEmpty,
  IsNumber,
  IsArray,
  ArrayNotEmpty,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateOrderReviewDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  product_id!: number;

  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  order_ids!: string[];

  @IsOptional()
  @Type(() => Number)
  customer_id!: number;
}

export class UpdateOrderReviewResponseDto {
  success: boolean;
  message: string;
  updated_items_count: number;
  affected_orders: string[];
  error?: string;
}
