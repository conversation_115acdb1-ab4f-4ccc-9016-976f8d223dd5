// order-tracking.module.ts

import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderTrackingController } from './order-tracking.controller';
import { AdminOrderTrackingController } from './admin-order-tracking.controller';
import { OrderTrackingModule } from '../shared/modules/order-tracking/order-tracking.module';
import { OrderTrackingService } from './order-tracking.service';
import { AdminOrderTrackingService } from './admin-order-tracking.service';
import { VinculumShipmentStatusModule } from 'src/shared/modules/vinculum-shipment-status/vinculum.module';
import { ExternalAPiModule } from 'src/shared/modules/external-api-caller/external-api-module';
import { OrderResponseMapper } from './mapper/order-response.helper';
import { AdminOrderResponseMapper } from './mapper/admin-order-response.helper';
import { OrderConsumerModule } from '../lambda/order-consumer/order-consumer.module';
import { OrderTrackingItem } from '../shared/modules/order-tracking/entities/order-tracking-item.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([OrderTrackingItem]),
    OrderTrackingModule,
    forwardRef(() => VinculumShipmentStatusModule),
    ExternalAPiModule,
    OrderConsumerModule,
  ],

  providers: [
    OrderTrackingService,
    OrderResponseMapper,
    AdminOrderTrackingService,
    AdminOrderResponseMapper,
  ],
  controllers: [OrderTrackingController, AdminOrderTrackingController],
  exports: [OrderTrackingService, AdminOrderTrackingService],
})
export class OrderTrackingRestAPIModule {}
