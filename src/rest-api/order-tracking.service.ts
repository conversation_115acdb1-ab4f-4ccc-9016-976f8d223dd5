import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  <PERSON><PERSON>ty<PERSON>ana<PERSON>,
  ILike,
  IsNull,
  Like,
  MoreThan<PERSON>rEqual,
  Not,
  Repository,
} from 'typeorm';
import { OrderTracking } from '../shared/modules/order-tracking/entities/order-tracking.entity';
import { OrderTrackingItem } from '../shared/modules/order-tracking/entities/order-tracking-item.entity';

import {
  OrderStatusResponseDto,
  StatusEvent,
} from '../shared/modules/order-tracking/dto/order-status-response-dto';
import { OrderStatus } from '../shared/modules/order-tracking/enums/order-status.enum';
import { ClickpostShipmentScan } from '../shared/modules/clickpost-shipment-scan/entities/clickpost-shipment-scan.entity';
import { VinculumShipmentStatus } from 'src/shared/modules/vinculum-shipment-status/entities/vinculum-shipment-status.entity';
import {
  DeliveryInfoDTO,
  OrderResponseDTO,
} from 'src/shared/modules/order-tracking/dto/shipment-dto';
import {
  generateShipmentHistory,
  nonProcessableStatus,
  shipmentToHistoryMap,
} from 'src/shared/modules/order-tracking/utils/order-status.utils';
import { ShipmentStatus } from 'src/shared/modules/vinculum-shipment-status/enums/shipment-status.enum';
import { VinculumShipmentStatusService } from 'src/shared/modules/vinculum-shipment-status/vinculum-shipment-status.service';
import { normalizeDate, addDaysToDate } from './utills/date.helper';
import {
  GetCustomersOrdersQueryParams,
  TimeUnit,
} from './dtos/order-list-param-dto';
import { ExternalApiHelper } from 'src/shared/modules/external-api-caller/external-service-helper';
import { OrderResponseMapper } from './mapper/order-response.helper';
import {
  isOrderBeforeDate,
  PROCESSING_ORDER_THRESHHOLD_DATE,
} from 'src/shared/utils/order-status-util';
import { LatestDeliveredOrderResponseDto } from './dtos/latest-delivered-order.dto';

@Injectable()
export class OrderTrackingService {
  constructor(
    @InjectRepository(OrderTracking)
    private orderTrackingRepository: Repository<OrderTracking>,
    @InjectRepository(OrderTrackingItem)
    private orderTrackingItemRepository: Repository<OrderTrackingItem>,
    private readonly entityManager: EntityManager,
    @Inject(forwardRef(() => VinculumShipmentStatusService))
    private readonly vinculumShipmentStatusService: VinculumShipmentStatusService,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly OrderResponseMapper: OrderResponseMapper,
  ) {}

  findAll(): Promise<OrderTracking[]> {
    return this.orderTrackingRepository.find();
  }

  findOne(orderId: string): Promise<OrderTracking | null> {
    return this.orderTrackingRepository.findOneBy({ orderId });
  }

  async remove(orderId: string): Promise<void> {
    await this.orderTrackingRepository.delete(orderId);
  }

  async create(orderData: any): Promise<OrderTracking> {
    if (
      !orderData.order_id ||
      !orderData.customer_id ||
      !orderData.order_date
    ) {
      throw new BadRequestException('Missing required fields');
    }

    const order = this.orderTrackingRepository.create({
      orderId: orderData.order_id,
      customerId: orderData.customer_id.toString(),
      orderDate: new Date(orderData.order_date),
      lineItemCount: orderData.items?.length || 0,
      latestStatus: OrderStatus.ORDER_RECEIVED,
      wmsVendor: 'VIN',
      logisticsVendor: 'CLP',
    });

    return await this.orderTrackingRepository.save(order);
  }

  async getOrderStatus(orderId: string): Promise<OrderStatusResponseDto> {
    const orderTracking = await this.orderTrackingRepository.findOne({
      where: { orderId },
      relations: [
        'vinculumShipmentStatuses',
        'vinculumShipmentStatuses.clickpostShipmentScans',
      ],
      order: {
        vinculumShipmentStatuses: {
          clickpostShipmentScans: {
            scan_timestamp: 'DESC',
          },
        },
      },
    });

    if (!orderTracking) {
      throw new BadRequestException(`Order with ID "${orderId}" not found`);
    }

    const response = new OrderStatusResponseDto();
    response.order_id = orderTracking.orderId;
    response.customer_id = orderTracking.customerId;
    response.order_date = orderTracking.orderDate.toISOString();
    response.latest_order_status = orderTracking.latestStatus;

    response.shipments = (orderTracking.vinculumShipmentStatuses ?? []).map(
      (shipment) => ({
        awb_number: shipment.awb_number ?? '',
        courier_partner: {
          id: shipment.courier_partner_id ?? -1,
          name: shipment.courier_partner_name ?? '',
        },
        latest_status: shipment.overallStatus,
        items: (shipment.items ?? []).map((item) => ({
          sku: item.sku,
          imageUrl: item.imageUrl,
          itemName: item.itemName,
          price: item.price,
          order_qty: item.order_qty,
        })),
        status_events: this.mapStatusEvents(shipment),
      }),
    );

    return response;
  }

  private mapStatusEvents(shipment: VinculumShipmentStatus) {
    const events: any[] = [];

    // Add allocation event if exists
    if (shipment.is_allocated && shipment.allocation_date) {
      events.push({
        type: 'Allocated',
        timestamp: shipment.allocation_date.toISOString(),
        sub_events: [
          {
            type: 'Allocated',
            timestamp: shipment.allocation_date.toISOString(),
            location: '',
            meta: {
              status_message: 'Order allocated for processing',
              event_type: 'allocation',
            },
          },
        ],
        meta: [],
        remark: 'Order allocated for processing',
      });
    }

    // Add pick event if exists
    if (shipment.is_picked && shipment.pick_date) {
      events.push({
        type: 'Picked',
        timestamp: shipment.pick_date.toISOString(),
        sub_events: [
          {
            type: 'Picked',
            timestamp: shipment.pick_date.toISOString(),
            location: '',
            meta: {
              status_message: 'Items picked from warehouse',
              event_type: 'pick',
            },
          },
        ],
        meta: [],
        remark: 'Items picked from warehouse',
      });
    }

    // Add pack event if exists
    if (shipment.is_packed && shipment.pack_date) {
      events.push({
        type: 'Packed',
        timestamp: shipment.pack_date.toISOString(),
        sub_events: [
          {
            type: 'Packed',
            timestamp: shipment.pack_date.toISOString(),
            location: '',
            meta: {
              status_message: 'Items packed for shipping',
              event_type: 'pack',
            },
          },
        ],
        meta: [],
        remark: 'Items packed for shipping',
      });
    }

    // Add cancel event if exists
    if (shipment.is_cancelled && shipment.cancel_date) {
      events.push({
        type: 'Cancelled',
        timestamp: shipment.cancel_date.toISOString(),
        sub_events: [
          {
            type: 'Cancelled',
            timestamp: shipment.cancel_date.toISOString(),
            location: '',
            meta: {
              status_message: 'Order cancelled',
              event_type: 'cancel',
            },
          },
        ],
        meta: [],
        remark: 'Order cancelled',
      });
    }

    // Add shipping events if they exist
    if (
      shipment.clickpostShipmentScans &&
      shipment.clickpostShipmentScans.length > 0
    ) {
      // Sort scans by timestamp in descending order
      const sortedScans = [...shipment.clickpostShipmentScans].sort(
        (a, b) =>
          new Date(b.scan_timestamp).getTime() -
          new Date(a.scan_timestamp).getTime(),
      );

      // Group sorted scans by their bucket
      const groupedScans = sortedScans.reduce(
        (acc, scan) => {
          const bucket = scan.clickpost_status_bucket_description;
          if (!acc[bucket]) {
            acc[bucket] = [];
          }
          acc[bucket].push(scan);
          return acc;
        },
        {} as Record<string, ClickpostShipmentScan[]>,
      );

      // Convert grouped scans into events
      Object.entries(groupedScans).forEach(([bucket, scans]) => {
        events.push({
          type: bucket,
          timestamp: scans[0].scan_timestamp.toISOString(),
          sub_events: scans.map((scan) => ({
            type: scan.clickpost_status_description,
            timestamp: scan.scan_timestamp.toISOString(),
            location: scan.location,
            meta: {
              status_message: scan.status_message,
              scan_id: scan.scan_id,
              remark: scan.remark,
            },
          })),
          meta: [],
          remark: scans[0].remark,
        });
      });
    }

    // Sort all events by timestamp in descending order (most recent first)
    return events.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );
  }

  async orderShipmentAndTrackHistory(orderId: string) {
    const orderTracking = await this.orderTrackingRepository.findOne({
      where: { orderId },
      relations: [
        'vinculumShipmentStatuses',
        'orderTrackingItem',
        'vinculumShipmentStatuses.clickpostShipmentScans',
      ],
      order: {
        vinculumShipmentStatuses: {
          clickpostShipmentScans: {
            scan_timestamp: 'DESC',
          },
        },
      },
    });

    if (!orderTracking) {
      const data =
        await this.externalApiHelper.oldOrderTrackingShippments(orderId);

      if (!data) {
        throw new BadRequestException('Tracking Info does not exit');
      }
      return data;
    }

    if (
      isOrderBeforeDate(
        orderTracking.orderDate,
        PROCESSING_ORDER_THRESHHOLD_DATE,
      )
    ) {
      const data = await this.externalApiHelper.oldOrderTrackingShippments(
        orderTracking.orderId,
      );

      if (!data) {
        throw new BadRequestException('Tracking Info does not exit');
      }
      return data;
    }

    if (!orderTracking.createAtStoreDate) {
      const createAtStoreDate = await this.saveVinCreatedAtDate(
        orderTracking.orderId,
        orderTracking.id,
      );

      if (createAtStoreDate) {
        orderTracking.createAtStoreDate = createAtStoreDate;
      }
    }

    const orderDate = orderTracking.orderDate.toISOString();

    const shipmentResposne: OrderResponseDTO = {
      order_date: orderDate,
      order_id: orderTracking.orderId,
      packages: [],
      shipment_header_text: null,
    };

    const EventsDateObj = {
      'Order Placed': orderDate,
      Packed: '',
      Shipped: '',
      Delivered: '',
      Lost: '',
      Damaged: '',
    };

    const {
      createAtStoreDate,
      expDeliveryDays,
      expDispatchDays,
      latestStatus,
    } = orderTracking;

    if (!orderTracking?.vinculumShipmentStatuses?.length) {
      const currentStatus = this.getShipmentStatus(latestStatus);

      EventsDateObj['Cancelled'] =
        orderTracking?.updatedAt?.toISOString() ?? '';

      shipmentResposne.packages.push({
        delivery_info: this.getDeliveryText(
          createAtStoreDate,
          expDeliveryDays,
          expDispatchDays,
          null,
          currentStatus as ShipmentStatus,
        ),
        expected_delivery_time: this.getExpectedDeliveryDate(
          createAtStoreDate,
          expDeliveryDays,
          currentStatus as ShipmentStatus,
        ),
        items: orderTracking.orderTrackingItem.map((data) => ({
          image: data.image,
          name: data.name,
          price: data.item_extra_info?.price_incl_tax,
          qty_ordered: data.ordered_qty,
          sku: data.sku,
          url_key: data.url_key,
        })),
        status:
          currentStatus === ShipmentStatus.PROCESSING
            ? 'Order Placed'
            : currentStatus,
        status_history: generateShipmentHistory(currentStatus, EventsDateObj),
        tracking_number: null,
        tracking_url: null,
        transporter: null,
      });

      return shipmentResposne;
    }

    if (orderTracking.vinculumShipmentStatuses.length > 1) {
      shipmentResposne.shipment_header_text = `Note! Your Order Is Split Into Multiple Shipment For Faster Delivery`;
    }

    const imageUrlMap = orderTracking.orderTrackingItem.reduce(
      (acc, item) => {
        acc[item.sku] = {
          image: item?.image
            ? item?.image?.startsWith('/')
              ? `https://images.dentalkart.com/media/catalog/product${item.image}`
              : ''
            : null,
          urlKey: item?.url_key,
        };
        return acc;
      },
      {} as {
        [sku: string]: {
          image: string;
          urlKey: string;
        };
      },
    );

    orderTracking.vinculumShipmentStatuses.forEach(
      (shipment: VinculumShipmentStatus) => {
        let currentStatus = shipment.overallStatus;

        if (shipment.cancel_date) {
          EventsDateObj['Cancelled'] =
            shipment?.cancel_date?.toISOString() ?? '';
        }

        if (shipment.pack_date) {
          EventsDateObj['Packed'] = shipment?.pack_date?.toISOString() ?? '';
        }

        if (shipment.ship_date) {
          EventsDateObj['Shipped'] = shipment?.ship_date?.toISOString();
        } else {
          EventsDateObj['Shipped'] =
            shipment.clickpostShipmentScans
              ?.find((d) => d.clickpost_status_bucket === 1)
              ?.scan_timestamp?.toISOString() ?? '';
        }

        if (currentStatus === ShipmentStatus.RETURNED) {
          EventsDateObj['Returned'] = shipment.clickpostShipmentScans
            ?.find((d) => d.clickpost_status_bucket === 7)
            ?.scan_timestamp?.toISOString();
        }

        if (currentStatus === ShipmentStatus.LOST) {
          EventsDateObj['Lost'] = shipment.clickpostShipmentScans
            ?.find((d) => d.clickpost_status_bucket === 8)
            ?.scan_timestamp?.toISOString();
        }

        if (currentStatus === ShipmentStatus.DAMAGED) {
          EventsDateObj['Damaged'] = shipment.clickpostShipmentScans
            ?.find((d) => d.clickpost_status_bucket === 9)
            ?.scan_timestamp?.toISOString();
        }

        if (currentStatus === ShipmentStatus.DELIVERED) {
          EventsDateObj['Delivered'] = shipment.clickpostShipmentScans
            ?.find((d) => d.clickpost_status_bucket === 6)
            ?.scan_timestamp?.toISOString();
        }

        if (currentStatus === ShipmentStatus.OUT_FOR_DELIVERY) {
          currentStatus = ShipmentStatus.SHIPPED;
        }

        if (orderTracking.latestStatus === OrderStatus.CANCELLED) {
          currentStatus = ShipmentStatus.CANCELLED;
        }

        shipmentResposne.packages.push({
          status: shipmentToHistoryMap[currentStatus] ?? currentStatus,
          delivery_info: this.getDeliveryText(
            createAtStoreDate,
            expDeliveryDays,
            expDispatchDays,
            shipment.ship_date,
            currentStatus,
          ),
          expected_delivery_time: this.getExpectedDeliveryDate(
            createAtStoreDate,
            expDeliveryDays,
            currentStatus,
          ),
          items: Array.from(
            (shipment?.items ?? [])
              .reduce((acc, data) => {
                const sku = data.sku;
                // If the SKU is not already in the accumulator, add the item
                if (!acc.has(sku)) {
                  acc.set(sku, {
                    image: imageUrlMap[sku]?.image ?? '',
                    name: data.itemName,
                    price: +data.price,
                    sku: sku,
                    qty_ordered: +data.order_qty,
                    url_key: imageUrlMap[sku]?.urlKey ?? '',
                  });
                }

                return acc;
              }, new Map())
              .values(),
          ),
          status_history: generateShipmentHistory(currentStatus, EventsDateObj),
          tracking_number: shipment.awb_number ?? null,
          tracking_url: shipment.awb_number
            ? `https://dentalkart.clickpost.ai/?waybill=${shipment.awb_number}`
            : '',
          transporter: shipment?.courier_partner_name ?? null,
        });
      },
    );

    return shipmentResposne;
  }

  async updateBulkOrders(ordersToUpdate: OrderTracking[]) {
    return this.entityManager.transaction(
      async (transactionalEntityManager) => {
        try {
          await transactionalEntityManager.save(OrderTracking, ordersToUpdate);
        } catch (error) {
          console.error(
            'Error updating  bulk orders:',
            error,
            JSON.stringify(ordersToUpdate),
          );
          return;
        }
      },
    );
  }

  async saveVinCreatedAtDate(orderId: string, trackingId: number) {
    try {
      const erpCreatedDate: Record<string, Date> = {};
      await this.vinculumShipmentStatusService.fetchVinculumStatus(
        [orderId],
        erpCreatedDate,
      );

      if (erpCreatedDate[orderId]) {
        await this.orderTrackingRepository.update(
          {
            id: trackingId,
          },
          {
            createAtStoreDate: erpCreatedDate[orderId],
          },
        );

        return erpCreatedDate[orderId];
      }
    } catch (e) {
      console.log(`Failed to update createAtStoreDate for order ${orderId}`);
    }
  }

  getDeliveryText(
    orderDateStr: Date,
    expectedDeliveryDays: number,
    expectedDispatchDays: number,
    shippedDateStr: Date,
    status: ShipmentStatus,
  ): DeliveryInfoDTO {
    try {
      const message: DeliveryInfoDTO = {
        code: null,
        label: null,
      };

      if (!orderDateStr) return message;

      if (nonProcessableStatus(status)) {
        return message;
      }

      if (status === ShipmentStatus.DELIVERED) {
        message.code = 'delivered_pckg';
        message.label = 'Package Was Handed To Resident';
        return message;
      }

      if (!expectedDeliveryDays) {
        return message;
      }

      const currentDate = normalizeDate();
      orderDateStr = normalizeDate(orderDateStr);

      const shippedDate = shippedDateStr ? normalizeDate(shippedDateStr) : null;

      const expectedDeliveryDate = addDaysToDate(
        orderDateStr,
        expectedDeliveryDays,
      );

      if (currentDate.getTime() < expectedDeliveryDate.getTime()) {
        message.label = 'Arriving On Time';
        message.code = 'pckg_on_time';
        if (shippedDate && expectedDispatchDays) {
          const expectedDispatchDate = addDaysToDate(
            orderDateStr,
            expectedDispatchDays,
          );

          if (shippedDate.getTime() < expectedDispatchDate.getTime()) {
            message.label = 'Arriving Early';
            message.code = 'early_shipped_pckg';
          }
        }
        return message;
      }

      const delayDays = Math.ceil(
        (currentDate.getTime() - expectedDeliveryDate.getTime()) /
          (1000 * 60 * 60 * 24),
      );

      message.label = `Delayed By ${delayDays} Days`;
      message.code = 'delayed_pckg';

      return message;
    } catch (e) {
      console.log('Error in getDeliveryText function', e);
      return { code: null, label: null };
    }
  }

  async getCustomerOrderList(
    queryParam: GetCustomersOrdersQueryParams,
    req: Request,
  ) {
    try {
      const {
        status,
        order_increment_id,
        item_name,
        time_unit,
        time_value,
        page_no = 1,
        page_size = 20,
        sort_by = 'orderDate,desc',
      } = queryParam;

      const authToken = this.getUserToken(req.headers['authorization']);
      if (!authToken) {
        throw new UnauthorizedException(
          'The request is allowed for only logged in customer',
        );
      }

      const customerExists =
        await this.externalApiHelper.getCustomerDetails(authToken);

      if (!customerExists.customer_id)
        throw new BadRequestException('Invalid auth token');

      // Check if customer has any old orders (before Feb 5th, 2025)
      const hasOldOrders =
        await this.externalApiHelper.checkCustomerOrderExists(
          customerExists.customer_id,
        );

      const [, sortOrder] = sort_by.split(',');

      const sortDirection = sortOrder === 'asc' ? 'ASC' : 'DESC';

      const fromDate = normalizeDate();
      if (time_unit === TimeUnit.Day) {
        fromDate.setUTCDate(fromDate.getUTCDate() - time_value);
      } else if (time_unit === TimeUnit.Month) {
        fromDate.setUTCMonth(fromDate.getUTCMonth() - time_value);
      } else if (time_unit === TimeUnit.Year) {
        fromDate.setUTCFullYear(fromDate.getUTCFullYear() - time_value);
      } else {
        fromDate.setUTCMonth(fromDate.getUTCMonth() - time_value);
      }

      // Define query conditions
      const whereCondition = {
        customerId: customerExists.customer_id.toString(),
        orderDate: MoreThanOrEqual(fromDate),
        ...(status && { latestStatus: status }),
        ...(order_increment_id && {
          orderId: ILike(`%${order_increment_id}%`),
        }),
      };

      // Define relations
      const relations = { orderTrackingItem: true };

      // Fetch orders with filtering and pagination
      const [orders, totalOrderCount] =
        await this.orderTrackingRepository.findAndCount({
          where: {
            ...whereCondition,
            ...(item_name
              ? {
                  orderTrackingItem: {
                    name: Like(`%${item_name.trim()}%`),
                  },
                }
              : {}),
          },
          relations,
          order: { orderDate: sortDirection },
          skip: (page_no - 1) * page_size,
          take: page_size,
        });

      // Pagination details
      const hasMoreOrders = totalOrderCount > page_no * page_size;
      const nextPage = hasMoreOrders ? page_no + 1 : 0;

      return this.OrderResponseMapper.buildCustomerOrderResponse(
        orders,
        totalOrderCount,
        page_size,
        page_no,
        nextPage,
        hasMoreOrders,
        hasOldOrders,
      );
    } catch (e) {
      if (e instanceof UnauthorizedException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e);
      }
    }
  }

  /**
   * Validate authorization header token
   * @param authHeader string
   * @returns
   */
  private getUserToken(authHeader: string) {
    const token = authHeader?.split(' ')[1];
    if (token === 'null') return undefined;
    return token;
  }

  private getExpectedDeliveryDate(
    orderDateStr: Date,
    expectedDeliveryDays: number,
    status: ShipmentStatus,
  ) {
    try {
      if (!expectedDeliveryDays || !orderDateStr) return null;
      if (nonProcessableStatus(status)) {
        return null;
      }
      orderDateStr = normalizeDate(orderDateStr);
      const resultDate = addDaysToDate(orderDateStr, expectedDeliveryDays);
      const formattedDate = resultDate.toISOString().split('T')[0];
      return formattedDate;
    } catch (e) {
      return null;
    }
  }

  getShipmentStatus(latestStatus: OrderStatus) {
    if (latestStatus === OrderStatus.CANCELLED) {
      return ShipmentStatus.CANCELLED;
    }
    if (latestStatus === OrderStatus.PAYMENT_PENDING) {
      return ShipmentStatus.PAYMENT_PENDING;
    }
    return ShipmentStatus.PROCESSING;
  }

  async getLatestDeliveredOrder(
    req: Request,
  ): Promise<LatestDeliveredOrderResponseDto> {
    try {
      // Get customer ID from auth token
      const authToken = this.getUserToken(req.headers['authorization']);
      if (!authToken) {
        throw new UnauthorizedException(
          'The request is allowed for only logged in customer',
        );
      }

      const customerDetails =
        await this.externalApiHelper.getCustomerDetails(authToken);

      if (!customerDetails.customer_id) {
        throw new BadRequestException('Invalid auth token');
      }

      // Find the latest delivered order using ORM
      const latestDeliveredOrder = await this.orderTrackingRepository.findOne({
        select: ['orderId', 'deliveredAt', 'lineItemCount'],
        where: {
          customerId: customerDetails.customer_id.toString(),
          latestStatus: OrderStatus.DELIVERED,
          deliveredAt: Not(IsNull()),
        },
        order: {
          deliveredAt: 'DESC',
        },
      });

      if (!latestDeliveredOrder) {
        return {
          success: true,
          show_rating_modal: false,
          message: 'No delivered orders found',
        };
      }

      // Check if any items have been reviewed using ORM count
      const reviewedItemsCount = await this.orderTrackingItemRepository.count({
        where: {
          order_id: latestDeliveredOrder.orderId,
          is_reviewed: true,
        },
      });

      // If at least one product is reviewed, don't show modal
      const showRatingModal = reviewedItemsCount === 0;

      return {
        success: true,
        show_rating_modal: showRatingModal,
        order_id: latestDeliveredOrder.orderId,
        delivered_at: latestDeliveredOrder.deliveredAt
          ? new Date(latestDeliveredOrder.deliveredAt)
          : null,
        item_count: latestDeliveredOrder.lineItemCount,
        message: showRatingModal
          ? 'Latest delivered order found - show rating modal'
          : 'Latest delivered order found - products already reviewed',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      return {
        success: false,
        show_rating_modal: false,
        message: 'Failed to retrieve latest delivered order',
        error: error.message,
      };
    }
  }
}
