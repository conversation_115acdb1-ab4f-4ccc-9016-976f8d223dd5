import { Test, TestingModule } from '@nestjs/testing';
import { OrderTrackingController } from './order-tracking.controller';
import { OrderTrackingService } from './order-tracking.service';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';

describe('OrderTrackingController - Latest Delivered Order', () => {
  let controller: OrderTrackingController;
  let service: OrderTrackingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrderTrackingController],
      providers: [
        {
          provide: OrderTrackingService,
          useValue: {
            getLatestDeliveredOrder: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<OrderTrackingController>(OrderTrackingController);
    service = module.get<OrderTrackingService>(OrderTrackingService);
  });

  describe('getLatestDeliveredOrder', () => {
    const mockRequest = {
      headers: {
        authorization: 'Bearer valid-token',
      },
    } as any;

    it('should return latest delivered order with show_rating_modal true when no products are reviewed', async () => {
      const mockResponse = {
        success: true,
        show_rating_modal: true,
        order: {
          order_id: '12345',
          delivered_at: new Date('2024-01-15'),
          grand_total: 1500.00,
          line_item_count: 2,
          items: [
            {
              product_id: 1,
              sku: 'SKU001',
              name: 'Product 1',
              image: 'image1.jpg',
              ordered_qty: 1,
              is_reviewed: false,
            },
            {
              product_id: 2,
              sku: 'SKU002',
              name: 'Product 2',
              image: 'image2.jpg',
              ordered_qty: 2,
              is_reviewed: false,
            },
          ],
        },
        message: 'Latest delivered order found - show rating modal',
      };

      jest.spyOn(service, 'getLatestDeliveredOrder').mockResolvedValue(mockResponse);

      const result = await controller.getLatestDeliveredOrder(mockRequest);

      expect(result).toBe(mockResponse);
      expect(result.show_rating_modal).toBe(true);
      expect(service.getLatestDeliveredOrder).toHaveBeenCalledWith(mockRequest);
    });

    it('should return latest delivered order with show_rating_modal false when at least one product is reviewed', async () => {
      const mockResponse = {
        success: true,
        show_rating_modal: false,
        order: {
          order_id: '12345',
          delivered_at: new Date('2024-01-15'),
          grand_total: 1500.00,
          line_item_count: 2,
          items: [
            {
              product_id: 1,
              sku: 'SKU001',
              name: 'Product 1',
              image: 'image1.jpg',
              ordered_qty: 1,
              is_reviewed: true, // This product is reviewed
            },
            {
              product_id: 2,
              sku: 'SKU002',
              name: 'Product 2',
              image: 'image2.jpg',
              ordered_qty: 2,
              is_reviewed: false,
            },
          ],
        },
        message: 'Latest delivered order found - products already reviewed',
      };

      jest.spyOn(service, 'getLatestDeliveredOrder').mockResolvedValue(mockResponse);

      const result = await controller.getLatestDeliveredOrder(mockRequest);

      expect(result).toBe(mockResponse);
      expect(result.show_rating_modal).toBe(false);
      expect(service.getLatestDeliveredOrder).toHaveBeenCalledWith(mockRequest);
    });

    it('should return no delivered orders found', async () => {
      const mockResponse = {
        success: true,
        show_rating_modal: false,
        message: 'No delivered orders found',
      };

      jest.spyOn(service, 'getLatestDeliveredOrder').mockResolvedValue(mockResponse);

      const result = await controller.getLatestDeliveredOrder(mockRequest);

      expect(result).toBe(mockResponse);
      expect(result.show_rating_modal).toBe(false);
      expect(service.getLatestDeliveredOrder).toHaveBeenCalledWith(mockRequest);
    });

    it('should throw UnauthorizedException for invalid token', async () => {
      jest
        .spyOn(service, 'getLatestDeliveredOrder')
        .mockRejectedValue(new UnauthorizedException('Invalid auth token'));

      await expect(
        controller.getLatestDeliveredOrder(mockRequest),
      ).rejects.toThrow(UnauthorizedException);
    });
  });
});
