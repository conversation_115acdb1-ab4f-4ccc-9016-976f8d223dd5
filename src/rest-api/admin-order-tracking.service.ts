import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Between,
  In,
  FindOptionsOrder,
  FindOptionsWhere,
  ILike,
  Repository,
} from 'typeorm';
import { OrderTracking } from '../shared/modules/order-tracking/entities/order-tracking.entity';
import { OrderTrackingItem } from '../shared/modules/order-tracking/entities/order-tracking-item.entity';
import { OrderStatus } from '../shared/modules/order-tracking/enums/order-status.enum';
import { AdminOrdersListQueryParams } from './dtos/admin-order-list-param.dto';
import { AdminOrderResponseMapper } from './mapper/admin-order-response.helper';
import { OrderConsumerService } from '../lambda/order-consumer/order-consumer.service';
import {
  ManualOrderConsumptionDto,
  ManualOrderConsumptionResponseDto,
} from './dtos/manual-order-consumption.dto';
import {
  UpdateOrderReviewDto,
  UpdateOrderReviewResponseDto,
} from './dtos/update-order-review.dto';

@Injectable()
export class AdminOrderTrackingService {
  private readonly logger = new Logger(AdminOrderTrackingService.name);

  constructor(
    @InjectRepository(OrderTracking)
    private readonly orderTrackingRepository: Repository<OrderTracking>,
    @InjectRepository(OrderTrackingItem)
    private readonly orderTrackingItemRepository: Repository<OrderTrackingItem>,
    private readonly adminOrderResponseMapper: AdminOrderResponseMapper,
    private readonly orderConsumerService: OrderConsumerService,
  ) {}

  async getAllOrders(queryParams: AdminOrdersListQueryParams) {
    const {
      page_no = 1,
      page_size = 20,
      sort_by = 'orderDate,desc',
      status,
      order_id,
      customer_id,
      item_name,
      start_date,
      end_date,
    } = queryParams;

    // Parse sort field and direction
    const [sortField, sortDirection] = sort_by.split(',');
    const sortOptions: FindOptionsOrder<OrderTracking> = {
      [sortField]: sortDirection.toLowerCase() === 'asc' ? 'ASC' : 'DESC',
    };

    // Build where conditions
    const whereConditions: FindOptionsWhere<OrderTracking> = {};

    if (status) {
      // Map client status to internal statuses
      const statusUpper = status.toUpperCase();
      let statuses: OrderStatus[];
      if (statusUpper === 'ORDER PLACED') {
        statuses = [OrderStatus.ORDER_RECEIVED, OrderStatus.PROCESSING];
      } else if (statusUpper === 'SHIPPED') {
        statuses = [OrderStatus.OUT_FOR_DELIVERY, OrderStatus.SHIPPED];
      } else {
        statuses = [status as OrderStatus];
      }
      whereConditions.latestStatus = In(statuses);
    }

    if (order_id) {
      whereConditions.orderId = ILike(`%${order_id}%`);
    }

    if (customer_id) {
      whereConditions.customerId = ILike(`%${customer_id}%`);
    }

    if (start_date && end_date) {
      whereConditions.orderDate = Between(
        new Date(start_date),
        new Date(end_date),
      );
    } else if (start_date) {
      whereConditions.orderDate = Between(new Date(start_date), new Date());
    } else if (end_date) {
      whereConditions.orderDate = Between(
        new Date('2000-01-01'),
        new Date(end_date),
      );
    }

    try {
      // Fetch orders with filtering and pagination
      const [orders, totalOrderCount] =
        await this.orderTrackingRepository.findAndCount({
          where: {
            ...whereConditions,
            ...(item_name
              ? {
                  orderTrackingItem: {
                    sku: ILike(`%${item_name.trim()}%`),
                  },
                }
              : {}),
          },
          order: sortOptions,
          skip: (page_no - 1) * page_size,
          take: page_size,
        });

      // Pagination details
      const hasMoreOrders = totalOrderCount > page_no * page_size;
      const nextPage = hasMoreOrders ? page_no + 1 : 0;

      return this.adminOrderResponseMapper.buildAdminOrderResponse(
        orders,
        totalOrderCount,
        page_size,
        page_no,
        nextPage,
        hasMoreOrders,
      );
    } catch (error) {
      console.error('Error fetching admin orders:', error);
      throw new BadRequestException('Failed to retrieve orders');
    }
  }

  async getOrderById(orderId: string) {
    try {
      const order = await this.orderTrackingRepository.findOne({
        where: { orderId },
        relations: { orderTrackingItem: true },
      });

      if (!order) {
        throw new BadRequestException(`Order with ID "${orderId}" not found`);
      }

      return this.adminOrderResponseMapper.buildAdminOrderResponse(
        [order],
        1,
        1,
        1,
        0,
        false,
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to retrieve order');
    }
  }

  async consumeOrderManually(
    orderConsumptionDto: ManualOrderConsumptionDto,
  ): Promise<ManualOrderConsumptionResponseDto> {
    try {
      this.logger.log(
        `Manual order consumption requested for order: ${orderConsumptionDto.orderData?.order_id || 'unknown'}`,
      );

      // Validate that order data contains required fields
      if (!orderConsumptionDto.orderData) {
        throw new BadRequestException('Order data is required');
      }

      if (!orderConsumptionDto.orderData.order_id) {
        throw new BadRequestException('Order ID is required in order data');
      }

      // Create a mock SQS record structure that the OrderConsumerService expects
      const mockSQSRecord = {
        body: JSON.stringify({
          detail: orderConsumptionDto.orderData,
        }),
        messageId: `manual-${Date.now()}`,
        receiptHandle: `manual-${Date.now()}`,
        attributes: {
          ApproximateReceiveCount: '1',
          SentTimestamp: Date.now().toString(),
          SenderId: 'manual-admin',
          ApproximateFirstReceiveTimestamp: Date.now().toString(),
        },
        messageAttributes: {},
        md5OfBody: '',
        eventSource: 'aws:sqs' as const,
        eventSourceARN: 'manual-consumption',
        awsRegion: 'manual',
      };

      // Process the order using the existing OrderConsumerService
      await this.orderConsumerService.processMessage(mockSQSRecord);

      this.logger.log(
        `Successfully processed manual order consumption for order: ${orderConsumptionDto.orderData.order_id}`,
      );

      return {
        success: true,
        message: 'Order consumed successfully',
        orderId: orderConsumptionDto.orderData.order_id,
      };
    } catch (error) {
      this.logger.error(
        `Error in manual order consumption: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: 'Failed to consume order',
        error: error.message,
        orderId: orderConsumptionDto.orderData?.order_id,
      };
    }
  }

  async updateOrderReviewStatus(
    updateOrderReviewDto: UpdateOrderReviewDto,
  ): Promise<UpdateOrderReviewResponseDto> {
    try {
      this.logger.log(
        `Updating review status for product_id: ${updateOrderReviewDto.product_id}, customer_id: ${updateOrderReviewDto.customer_id}, orders: ${updateOrderReviewDto.order_ids.join(', ')}`,
      );

      // Validate that orders exist and belong to the customer (optimized with select)
      const validOrders = await this.orderTrackingRepository.find({
        select: ['orderId'],
        where: {
          orderId: In(updateOrderReviewDto.order_ids),
          customerId: updateOrderReviewDto.customer_id.toString(),
        },
      });

      if (validOrders.length === 0) {
        return {
          success: false,
          message: 'No orders found for the given criteria',
          updated_items_count: 0,
          affected_orders: [],
          error: 'Orders not found or do not belong to the specified customer',
        };
      }

      const foundOrderIds = validOrders.map((order) => order.orderId);
      const missingOrderIds = updateOrderReviewDto.order_ids.filter(
        (orderId) => !foundOrderIds.includes(orderId),
      );

      if (missingOrderIds.length > 0) {
        this.logger.warn(
          `Some orders not found or don't belong to customer: ${missingOrderIds.join(', ')}`,
        );
      }

      // Check if any items with the specified product_id exist (optimized count query)
      const matchingItemsCount = await this.orderTrackingItemRepository.count({
        where: {
          order_id: In(foundOrderIds),
          product_id: updateOrderReviewDto.product_id,
        },
      });

      // Only update is_reviewed to true if NO items with this product_id are found
      if (matchingItemsCount === 0) {
        // Update all items in these orders to mark them as reviewed
        const updateResult = await this.orderTrackingItemRepository.update(
          {
            order_id: In(foundOrderIds),
          },
          {
            is_reviewed: true,
          },
        );

        this.logger.log(
          `No items found with product_id: ${updateOrderReviewDto.product_id}. Updated ${updateResult.affected} items as reviewed.`,
        );

        return {
          success: true,
          message: `Product not found in orders. Successfully marked ${updateResult.affected} items as reviewed`,
          updated_items_count: updateResult.affected || 0,
          affected_orders: foundOrderIds,
        };
      } else {
        this.logger.log(
          `Found ${matchingItemsCount} items with product_id: ${updateOrderReviewDto.product_id}. No update performed.`,
        );

        return {
          success: false,
          message: `Product found in orders. No items were marked as reviewed`,
          updated_items_count: 0,
          affected_orders: foundOrderIds,
          error: `Product with ID ${updateOrderReviewDto.product_id} exists in the specified orders`,
        };
      }
    } catch (error) {
      this.logger.error(
        `Error updating order review status: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: 'Failed to update order review status',
        updated_items_count: 0,
        affected_orders: [],
        error: error.message,
      };
    }
  }
}
