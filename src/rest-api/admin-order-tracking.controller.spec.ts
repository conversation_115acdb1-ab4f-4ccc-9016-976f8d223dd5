import { Test, TestingModule } from '@nestjs/testing';
import { AdminOrderTrackingController } from './admin-order-tracking.controller';
import { AdminOrderTrackingService } from './admin-order-tracking.service';
import { BadRequestException } from '@nestjs/common';

describe('AdminOrderTrackingController', () => {
  let controller: AdminOrderTrackingController;
  let service: AdminOrderTrackingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminOrderTrackingController],
      providers: [
        {
          provide: AdminOrderTrackingService,
          useValue: {
            getAllOrders: jest.fn(),
            getOrderById: jest.fn(),
            consumeOrderManually: jest.fn(),
            updateOrderReviewStatus: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AdminOrderTrackingController>(
      AdminOrderTrackingController,
    );
    service = module.get<AdminOrderTrackingService>(AdminOrderTrackingService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateOrderReviewStatus', () => {
    const mockUpdateDto = {
      product_id: 1,
      order_ids: ['234234', '45645'],
      customer_id: 123,
    };

    const mockSuccessResponse = {
      success: true,
      message: 'Successfully updated review status for 2 items',
      updated_items_count: 2,
      affected_orders: ['234234', '45645'],
    };

    beforeEach(() => {
      jest.clearAllMocks();
      jest
        .spyOn(service, 'updateOrderReviewStatus')
        .mockResolvedValue(mockSuccessResponse);
    });

    it('should update order review status successfully', async () => {
      const result = await controller.updateOrderReviewStatus(mockUpdateDto);

      expect(result).toBe(mockSuccessResponse);
      expect(service.updateOrderReviewStatus).toHaveBeenCalledWith(
        mockUpdateDto,
      );
    });

    it('should handle service errors gracefully', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Failed to update order review status',
        updated_items_count: 0,
        affected_orders: [],
        error: 'Database error',
      };

      jest
        .spyOn(service, 'updateOrderReviewStatus')
        .mockResolvedValue(mockErrorResponse);

      const result = await controller.updateOrderReviewStatus(mockUpdateDto);

      expect(result).toBe(mockErrorResponse);
      expect(service.updateOrderReviewStatus).toHaveBeenCalledWith(
        mockUpdateDto,
      );
    });

    it('should throw InternalServerErrorException for unexpected errors', async () => {
      jest
        .spyOn(service, 'updateOrderReviewStatus')
        .mockRejectedValue(new Error('Unexpected error'));

      await expect(
        controller.updateOrderReviewStatus(mockUpdateDto),
      ).rejects.toThrow('Failed to update order review status');
    });
  });
});
