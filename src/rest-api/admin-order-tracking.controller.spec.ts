import { Test, TestingModule } from '@nestjs/testing';
import { AdminOrderTrackingController } from './admin-order-tracking.controller';
import { AdminOrderTrackingService } from './admin-order-tracking.service';

describe('AdminOrderTrackingController', () => {
  let controller: AdminOrderTrackingController;
  let service: AdminOrderTrackingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminOrderTrackingController],
      providers: [
        {
          provide: AdminOrderTrackingService,
          useValue: {
            getAllOrders: jest.fn(),
            getOrderById: jest.fn(),
            consumeOrderManually: jest.fn(),
            updateOrderReviewStatus: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AdminOrderTrackingController>(
      AdminOrderTrackingController,
    );
    service = module.get<AdminOrderTrackingService>(AdminOrderTrackingService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateOrderReviewStatus', () => {
    const mockUpdateDto = {
      product_id: 1,
      order_ids: ['234234', '45645'],
      customer_id: 123,
    };

    const mockSuccessResponse = {
      success: true,
      message:
        'Product not found in orders. Successfully marked 5 items as reviewed',
      updated_items_count: 5,
      affected_orders: ['234234', '45645'],
    };

    beforeEach(() => {
      jest.clearAllMocks();
      jest
        .spyOn(service, 'updateOrderReviewStatus')
        .mockResolvedValue(mockSuccessResponse);
    });

    it('should update order review status successfully', async () => {
      const result = await controller.updateOrderReviewStatus(mockUpdateDto);

      expect(result).toBe(mockSuccessResponse);
      expect(service.updateOrderReviewStatus).toHaveBeenCalledWith(
        mockUpdateDto,
      );
    });

    it('should handle case when product is found in orders', async () => {
      const mockProductFoundResponse = {
        success: false,
        message: 'Product found in orders. No items were marked as reviewed',
        updated_items_count: 0,
        affected_orders: ['234234', '45645'],
        error: 'Product with ID 1 exists in the specified orders',
      };

      jest
        .spyOn(service, 'updateOrderReviewStatus')
        .mockResolvedValue(mockProductFoundResponse);

      const result = await controller.updateOrderReviewStatus(mockUpdateDto);

      expect(result).toBe(mockProductFoundResponse);
      expect(service.updateOrderReviewStatus).toHaveBeenCalledWith(
        mockUpdateDto,
      );
    });

    it('should handle service errors gracefully', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Failed to update order review status',
        updated_items_count: 0,
        affected_orders: [],
        error: 'Database error',
      };

      jest
        .spyOn(service, 'updateOrderReviewStatus')
        .mockResolvedValue(mockErrorResponse);

      const result = await controller.updateOrderReviewStatus(mockUpdateDto);

      expect(result).toBe(mockErrorResponse);
      expect(service.updateOrderReviewStatus).toHaveBeenCalledWith(
        mockUpdateDto,
      );
    });

    it('should throw InternalServerErrorException for unexpected errors', async () => {
      jest
        .spyOn(service, 'updateOrderReviewStatus')
        .mockRejectedValue(new Error('Unexpected error'));

      await expect(
        controller.updateOrderReviewStatus(mockUpdateDto),
      ).rejects.toThrow('Failed to update order review status');
    });
  });
});
