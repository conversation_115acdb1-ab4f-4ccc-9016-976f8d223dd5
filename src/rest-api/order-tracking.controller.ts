import {
  BadRequestException,
  Controller,
  Get,
  InternalServerErrorException,
  Param,
  Query,
  Req,
  UnauthorizedException,
  ValidationPipe,
} from '@nestjs/common';
import { OrderTrackingService } from './order-tracking.service';
import { OrderStatusResponseDto } from '../shared/modules/order-tracking/dto/order-status-response-dto';
import { OrderResponseDTO } from '../shared/modules/order-tracking/dto/shipment-dto';
import { GetCustomersOrdersQueryParams } from './dtos/order-list-param-dto';
import { LatestDeliveredOrderResponseDto } from './dtos/latest-delivered-order.dto';

@Controller('order-tracking/api/v1/orders')
export class OrderTrackingController {
  constructor(private readonly orderTrackingService: OrderTrackingService) {}

  @Get(':orderId/status')
  async getOrderStatus(
    @Param('orderId') orderId: string,
  ): Promise<OrderStatusResponseDto> {
    try {
      return await this.orderTrackingService.getOrderStatus(orderId);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to retrieve order status');
    }
  }
  @Get(':orderId/shipments')
  async getOrderShipments(
    @Param('orderId') orderId: string,
  ): Promise<OrderResponseDTO> {
    try {
      return await this.orderTrackingService.orderShipmentAndTrackHistory(
        orderId,
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to retrieve order status');
    }
  }

  @Get()
  async getCustomerOrderList(
    @Query(new ValidationPipe({ transform: true }))
    queryParam: GetCustomersOrdersQueryParams,
    @Req() req: Request,
  ) {
    try {
      const data = await this.orderTrackingService.getCustomerOrderList(
        queryParam,
        req,
      );
      return data;
    } catch (e) {
      if (
        e instanceof BadRequestException ||
        e instanceof UnauthorizedException
      ) {
        throw e;
      } else {
        console.log(e);
        throw new InternalServerErrorException(
          'Failed to retrieve customer order l',
        );
      }
    }
  }

  @Get('latest-delivered')
  async getLatestDeliveredOrder(
    @Req() req: Request,
  ): Promise<LatestDeliveredOrderResponseDto> {
    try {
      return await this.orderTrackingService.getLatestDeliveredOrder(req);
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      console.error('Error in getLatestDeliveredOrder:', error);
      throw new InternalServerErrorException(
        'Failed to retrieve latest delivered order',
      );
    }
  }
}
