import { Injectable } from '@nestjs/common';
import { OrderTracking } from 'src/shared/modules/order-tracking/entities/order-tracking.entity';
import { OrderStatus } from 'src/shared/modules/order-tracking/enums/order-status.enum';
import { getExpectedDeliveryDate } from '../utills/date.helper';
import { OrderTrackingItem } from 'src/shared/modules/order-tracking/entities/order-tracking-item.entity';
import { isTerminalStatus } from 'src/shared/modules/order-tracking/utils/order-status.utils';
import {
  PROCESSING_ORDER_THRESHHOLD_DATE,
  getOrderStatus,
  isOrderBeforeDate,
  orderStatus,
} from 'src/shared/utils/order-status-util';

@Injectable()
export class OrderResponseMapper {
  buildCustomerOrderResponse(
    orders: OrderTracking[],
    totalOrderCount: number,
    pageSize: number,
    pageNo: number,
    nextPage: number,
    hasMoreOrders: boolean,
    hasOldOrders: boolean,
  ) {
    return {
      has_old_orders: hasOldOrders,
      pagination: {
        next_page: nextPage,
        page_size: pageSize,
        page_no: pageNo,
      },
      total_order_count: totalOrderCount,
      has_more_orders: hasMoreOrders,
      currency: {
        code: 'INR',
        symbol: '₹',
      },
      orders: orders.map((order: OrderTracking) => {
        const isPrepaid =
          order.orderExtraInfo?.order_status === 'payment_received'
            ? true
            : false;
        const latestStatus = isOrderBeforeDate(
          order.orderDate,
          PROCESSING_ORDER_THRESHHOLD_DATE,
        )
          ? orderStatus(
              order?.orderExtraInfo?.erp_status,
              order?.orderExtraInfo?.order_status,
            )
          : getOrderStatus(
              order.latestStatus,
              order?.orderExtraInfo?.latest_status,
            );
        return {
          data: {
            dates: {
              ordered: {
                date: this.formatTimestamp(order.orderDate.toISOString()),
                text: `Ordered On - ${this.formatTimestamp(order.orderDate.toISOString())}`,
              },
              delivered: {
                date: this.formatTimestamp(
                  order?.orderExtraInfo?.deliveredAt ?? null,
                ),
                text: `Delivered  On - ${this.formatTimestamp(order?.orderExtraInfo?.deliveredAt ?? '')}`,
              },
              cancelled: {
                date: this.formatTimestamp(
                  order?.orderExtraInfo?.cancelledAt ?? null,
                ),
                text: `cancelled  On - ${this.formatTimestamp(order?.orderExtraInfo?.cancelledAt ?? '')}`,
              },
              returned: {
                date: this.formatTimestamp(
                  order?.orderExtraInfo?.returnedAt ?? null,
                ),
                text: `returned  On - ${this.formatTimestamp(order?.orderExtraInfo?.returnedAt ?? '')}`,
              },
              estimated_delivery_date: getExpectedDeliveryDate(
                order.createAtStoreDate,
                order.expDeliveryDays,
                latestStatus,
              ),
            },
            order_id: order.orderId,
            rewards: {
              earned_points: order?.orderExtraInfo?.reward_point_earned ?? 0,
              spent_points: order.orderExtraInfo?.reward_point_used ?? 0,
              reward_term: 'Coins',
              reward_icon: '/Reward_coin__1__RiaFlHd9r.png',
            },
            can_cancel:
              !isPrepaid &&
              [
                OrderStatus.PARTIALLY_PACKED,
                OrderStatus.PAYMENT_PENDING,
                OrderStatus.ORDER_RECEIVED,
                OrderStatus.PACKED,
                OrderStatus.PROCESSING,
                OrderStatus.ORDER_RECEIVED,
              ].includes(latestStatus),
            can_return: [
              OrderStatus.DELIVERED,
              OrderStatus.PARTIALLY_DELIVERED,
            ].includes(latestStatus),
            can_reorder: true,
            is_processing: isTerminalStatus(order.latestStatus) ? false : true,
            order_summary: {
              order_amount: order.grandTotal,
              ...this.buildItemsTotal(order?.orderTrackingItem ?? []),
            },
            status: {
              id: null,
              code: [
                OrderStatus.PROCESSING,
                OrderStatus.ORDER_RECEIVED,
              ].includes(latestStatus)
                ? 'Order Placed'
                : latestStatus,
              label: [
                OrderStatus.PROCESSING,
                OrderStatus.ORDER_RECEIVED,
              ].includes(latestStatus)
                ? 'Order Placed'
                : latestStatus,
            },
            cta_title: 'View Details',
            item_text: null,
            items: order.orderTrackingItem?.map((item) => ({
              name: item?.name,
              price: +item.item_extra_info?.price_incl_tax || null,
              ordered_qty: +item.ordered_qty || null,
              row_total: +item?.item_extra_info?.row_total_incl_tax || null,
              product_id: +item.product_id || null,
              status: {
                id: null,
                code: null,
                label: null,
              },
              sku: item.sku,
              thumbnail: item?.image
                ? item.image[0] === '/'
                  ? `https://images.dentalkart.com/media/catalog/product` +
                    item?.image
                  : item?.image
                : null,
              url_key: item?.url_key ?? null,
              reward_earned_coins:
                +item?.item_extra_info?.reward_points || null,
              is_free_product: item?.item_extra_info?.is_free_product
                ? true
                : false,
              parent_id: item?.item_extra_info?.parent_id ?? null,
              is_reviewed: item.is_reviewed || false,
            })),
            rate_info: this.buildRateInfo(order.orderTrackingItem || []),
          },
          tracking: {
            tracking_available: ![
              OrderStatus.PAYMENT_PENDING,
              OrderStatus.PAYMENT_FAILED,
            ].includes(latestStatus),
            track_title: 'Track',
            deeplink: `https://www.dentalkart.com/track-service/orders/order-id=${order.orderId}`,
          },
        };
      }),
    };
  }

  buildItemsTotal(orderItems: OrderTrackingItem[]) {
    const { unique_product_sku, total_product_qty, total_savings } =
      orderItems.reduce(
        (summary, item) => {
          summary.unique_product_sku.add(item.sku);
          summary.total_product_qty += +item.ordered_qty;
          return summary;
        },
        {
          unique_product_sku: new Set(),
          total_product_qty: 0,
          total_savings: 0,
        },
      );
    return {
      unique_product_count: unique_product_sku.size,
      total_product_qty,
      total_savings,
    };
  }

  buildRateInfo(items: any[]) {
    const totalCount = items.length;
    const ratedCount = items.filter((item) => item.is_reviewed === true).length;
    const isFullRated = totalCount > 0 && ratedCount === totalCount;

    return {
      rated_count: ratedCount,
      total_count: totalCount,
      is_full_rated: isFullRated,
    };
  }

  private formatTimestamp(isoString: string) {
    try {
      if (!isoString) return '';
      return isoString.replace('T', ' ').replace('Z', '');
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e: any) {
      return '';
    }
  }
}
