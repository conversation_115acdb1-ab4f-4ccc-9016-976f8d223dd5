import { BadRequestException, Injectable } from '@nestjs/common';
import { SalesOrder } from 'src/database/entities/sales-order';
import { SalesOrderExtraInfo } from 'src/database/entities/sales-order-extra-info';
import { Request } from 'express';
import { OrderHelper } from 'src/utils/order.helper';
import { OrderEventAction } from 'src/dto/order-event.dto';
import { orderStatuses } from 'src/database/seeder/data/order-statuses';
import { EVENT_THRESH_HOLD_DATE, OrderStatuses } from 'src/config/constants';

@Injectable()
export class OrderEventService {
  constructor(private readonly orderHelper: OrderHelper) {}

  async fireOrderEvent(
    order_id: string,
    action: OrderEventAction,
    req: Request,
  ): Promise<boolean> {
    try {
      const order = await SalesOrder.findOne({
        where: {
          increment_id: order_id,
        },
        include: [
          {
            model: SalesOrderExtraInfo,
            attributes: ['events_occurred', 'extra_info_entity_id'],
          },
        ],
      });

      if (!order) {
        throw new BadRequestException('Order not found');
      }

      //negate order before threshold
      if (order.createdAt.getTime() < EVENT_THRESH_HOLD_DATE.getTime()) {
        return false;
      }

      if (
        ![OrderStatuses.NEW_ORDER, OrderStatuses.PAYMENT_RECEIVED].includes(
          order.status,
        )
      ) {
        return false;
      }

      const extraInfo = order.sales_order_extra_info;

      if (extraInfo) {
        const eventStatus = extraInfo.events_occurred || [];

        //Event already fired
        if (eventStatus.includes(action)) {
          return false;
        }

        await SalesOrderExtraInfo.update(
          {
            events_occurred: [...eventStatus, action],
          },
          {
            where: { extra_info_entity_id: extraInfo.extra_info_entity_id },
          },
        );

        //udpate events and fire events to sendEasyInsightsData;
        this.orderHelper.sendEasyInsightsData(
          req,
          {
            order_id,
            action,
          },
          order.customer_id,
          'purchase',
        );

        return true;
      }

      // save events and fire events to sendEasyInsightsData;
      await SalesOrderExtraInfo.create({
        order_id: order.order_id,
        events_occurred: [action],
      });

      //send events to sendEasyInsightsData
      this.orderHelper.sendEasyInsightsData(
        req,
        {
          order_id,
          action,
        },
        order.customer_id,
        'purchase',
      );

      return true;
    } catch (e) {
      return false;
    }
  }
}
