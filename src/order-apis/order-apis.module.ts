import { Modu<PERSON> } from '@nestjs/common';
import { OrderApisService } from './order-apis.service';
import { OrderApisController } from './order-apis.controller';
import { OrderMapper } from '../mapper/order';
import { UtilsModule } from 'src/utils/utils.module';
import { RazorpayModule } from 'src/razorpay/razorpay.module';
import { DatabaseModule } from 'src/database/database.module';
import { OrderEventService } from './order-event.service';

@Module({
  imports: [UtilsModule, RazorpayModule, DatabaseModule],
  providers: [OrderApisService, OrderMapper, OrderEventService],
  controllers: [OrderApisController],
})
export class OrderApisModule {}
