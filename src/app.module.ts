import { OrderAdminController } from './order.controller';
import { Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { OrderResolver } from './order.resolver';
import { UtilsModule } from './utils/utils.module';
import { OrderHelper } from './utils/order.helper';
import { HttpModule } from '@nestjs/axios';
import { ExternalServiceCaller } from './utils/external-service-caller';
import { OrderMapper } from './mapper/order';
import { databaseProviders } from './database/database.provider';
import { NotificationHelper } from './utils/notification.helper';
import { ErpModule } from './erp/erp.module';
import { NotifyERP } from './utils/notify-erp';
import { RazorpayModule } from './razorpay/razorpay.module';
import { ProductResolver } from './product.resolver';
import { OrderAdminHelper } from './utils/order-admin.helper';
// import { CancelOrderModule } from './cancelOrder/cancle-order.module';
import { BuyNowPlaceOrder } from './buy-now.resolver';
import { OrderApisModule } from './order-apis/order-apis.module';
// import { SchedulerModule } from './scheduler/scheduler.module';

@Module({
  imports: [
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      playground: true,
      typePaths: ['./**/*.graphql'],
      definitions: {
        path: join(process.cwd(), 'src/graphql.ts'),
      },
    }),
    DatabaseModule,
    UtilsModule,
    HttpModule,
    ErpModule,
    RazorpayModule,
    OrderApisModule,
    // CancelOrderModule,
    // SchedulerModule,
  ],
  controllers: [OrderAdminController, AppController],
  providers: [
    AppService,
    OrderResolver,
    OrderHelper,
    ExternalServiceCaller,
    OrderMapper,
    NotificationHelper,
    NotifyERP,
    ProductResolver,
    OrderAdminHelper,
    ...databaseProviders,
    BuyNowPlaceOrder,
  ],
})
export class AppModule {}
