// Simple test to demonstrate the V3 order ID generation logic
// This shows how the 6-digit random number is converted to base-36

function testOrderIdV3Generation() {
  console.log('=== Order ID V3 Generation Test ===\n');

  // Simulate today's date
  const today = new Date();
  let day = today.getDate();
  let month = today.getMonth() + 1;
  const year = today.getFullYear();

  // Ensure day and month are two digits
  if (day < 10) day = `0${day}`;
  if (month < 10) month = `0${month}`;

  // Get last two digits of each component (DMY)
  const dayLastTwo = day.toString().slice(-2);
  const monthLastTwo = month.toString().slice(-2);
  const yearLastTwo = year.toString().slice(-2);

  // Convert each component to base 36 individually, then arrange as YMD
  const dayBase36 = parseInt(dayLastTwo, 10).toString(36).toUpperCase();
  const monthBase36 = parseInt(monthLastTwo, 10).toString(36).toUpperCase();
  const yearBase36 = parseInt(yearLastTwo, 10).toString(36).toUpperCase();

  // Arrange in reverse order: Year-Month-Day
  const ymdBase36 = `${yearBase36}${monthBase36}${dayBase36}`;

  console.log(`Date: ${day}/${month}/${year}`);
  console.log(`Day (${dayLastTwo}) -> Base36: ${dayBase36}`);
  console.log(`Month (${monthLastTwo}) -> Base36: ${monthBase36}`);
  console.log(`Year (${yearLastTwo}) -> Base36: ${yearBase36}`);
  console.log(`YMD Base36: ${ymdBase36}`);
  console.log('');

  // Generate 5 sample order IDs
  console.log('Sample Order IDs:');
  for (let i = 0; i < 5; i++) {
    // Generate 6 random digits and convert to base 36 with uppercase letters
    const randomSixDigits = Math.floor(100000 + Math.random() * 900000); // Ensures 6 digits
    const randomBase36 = randomSixDigits.toString(36).toUpperCase();

    // Create increment ID without dash separator
    const increment_id = `${ymdBase36}${randomBase36}`;

    console.log(
      `${
        i + 1
      }. ${increment_id} (Random: ${randomSixDigits} -> ${randomBase36})`,
    );
  }

  console.log('\n=== Format Explanation ===');
  console.log('Format: YMDRRRRRR');
  console.log('Y = Year (last 2 digits in base-36)');
  console.log('M = Month (base-36)');
  console.log('D = Day (base-36)');
  console.log('RRRRRR = 6-digit random number in base-36');
  console.log('');
  console.log('Base-36 uses: 0-9 and A-Z (36 characters total)');
  console.log('This provides much higher uniqueness than decimal numbers');
}

// Run the test
testOrderIdV3Generation();
